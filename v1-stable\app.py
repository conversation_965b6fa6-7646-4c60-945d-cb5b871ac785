"""
POS & Supplier Data Merger (V1 - STABLE)
A Streamlit application for merging POS and Supplier CSV files based on UPC column.
Features comprehensive logging and modern UI design.
VERSION: 1.0 - CSV ONLY (STABLE - DO NOT EDIT)
"""

import streamlit as st
import pandas as pd
import logging

# Configure logging
def setup_logging():
    """Configure logging to write to app.log file with INFO level."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app.log'),
            logging.StreamHandler()  # Also log to console for debugging
        ]
    )
    return logging.getLogger(__name__)

# Initialize logger
logger = setup_logging()

def apply_custom_css():
    """Apply custom CSS for modern, professional styling."""
    st.markdown("""
    <style>
    /* Main app styling */
    .main {
        padding-top: 2rem;
    }
    
    /* Title styling */
    .main-title {
        text-align: center;
        color: #0047AB;
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .subtitle {
        text-align: center;
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }
    
    /* Instructions styling */
    .instructions {
        background: linear-gradient(135deg, #f0f2f6 0%, #e8eaf6 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #0047AB;
        margin-bottom: 2rem;
    }
    
    /* File upload sections */
    .upload-section {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 2px solid #f0f2f6;
        transition: border-color 0.3s ease;
    }
    
    .upload-section:hover {
        border-color: #0047AB;
    }
    
    /* Success message styling */
    .success-box {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin: 1rem 0;
        font-weight: 600;
    }
    
    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #FFA500 0%, #ff8c00 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255,165,0,0.3);
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255,165,0,0.4);
    }
    
    /* Download button styling */
    .stDownloadButton > button {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(76,175,80,0.3);
    }
    
    .stDownloadButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(76,175,80,0.4);
    }
    
    /* Data preview styling */
    .dataframe {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    </style>
    """, unsafe_allow_html=True)

def convert_df_to_csv(df):
    """Convert DataFrame to CSV format for download."""
    return df.to_csv(index=False).encode('utf-8')

def process_and_merge_data(pos_file, supplier_file):
    """
    Process and merge POS and Supplier data files.

    Args:
        pos_file: Uploaded POS CSV file
        supplier_file: Uploaded Supplier CSV file

    Returns:
        tuple: (merged_dataframe, success_flag, error_message)
    """
    try:
        logger.info("Starting data merge process...")

        # Read CSV files into DataFrames with encoding handling
        # Try multiple encodings to handle different file formats
        encodings_to_try = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']

        pos_df = None
        supplier_df = None

        # Try to read POS file with different encodings
        for encoding in encodings_to_try:
            try:
                # Reset file pointer to beginning
                pos_file.seek(0)
                pos_df = pd.read_csv(pos_file, encoding=encoding, encoding_errors='replace')
                logger.info(f"POS file successfully read with {encoding} encoding")
                break
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                logger.warning(f"Error reading POS file with {encoding}: {str(e)}")
                continue

        if pos_df is None:
            raise ValueError("Could not read POS file with any supported encoding. Please ensure the file is a valid CSV.")

        # Try to read Supplier file with different encodings
        for encoding in encodings_to_try:
            try:
                # Reset file pointer to beginning
                supplier_file.seek(0)
                supplier_df = pd.read_csv(supplier_file, encoding=encoding, encoding_errors='replace')
                logger.info(f"Supplier file successfully read with {encoding} encoding")
                break
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                logger.warning(f"Error reading Supplier file with {encoding}: {str(e)}")
                continue

        if supplier_df is None:
            raise ValueError("Could not read Supplier file with any supported encoding. Please ensure the file is a valid CSV.")
        
        logger.info(f"POS data loaded: {pos_df.shape[0]} rows, {pos_df.shape[1]} columns")
        logger.info(f"Supplier data loaded: {supplier_df.shape[0]} rows, {supplier_df.shape[1]} columns")
        
        # Check if UPC column exists in both files
        if 'UPC' not in pos_df.columns:
            raise ValueError("UPC column not found in POS file")
        if 'UPC' not in supplier_df.columns:
            raise ValueError("UPC column not found in Supplier file")
        
        # Critical step: Convert UPC columns to string type to handle data type mismatches
        pos_df['UPC'] = pos_df['UPC'].astype(str)
        supplier_df['UPC'] = supplier_df['UPC'].astype(str)
        
        logger.info("UPC columns converted to string type for accurate matching")
        
        # Perform inner merge on UPC column
        merged_df = pd.merge(pos_df, supplier_df, on='UPC', how='inner')
        
        logger.info(f"Data merge successful. Merged data has {merged_df.shape[0]} rows and {merged_df.shape[1]} columns.")
        
        return merged_df, True, None
        
    except Exception as e:
        error_msg = f"Error during data merge process: {str(e)}"
        logger.error(error_msg)
        return None, False, error_msg

def main():
    """Main application function."""
    # Page configuration MUST be the first Streamlit command
    st.set_page_config(
        page_title="POS & Supplier Data Merger",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # Log application start
    logger.info("Application session started.")

    # Apply custom styling
    apply_custom_css()
    
    # Main title and subtitle
    st.markdown('<h1 class="main-title">📊 POS & Supplier Data Merger</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Merge your POS and Supplier CSV files seamlessly</p>', unsafe_allow_html=True)
    
    # Instructions
    st.markdown("""
    <div class="instructions">
        <h3>📋 How to Use:</h3>
        <p><strong>Step 1:</strong> Upload both your POS and Supplier CSV files (must contain UPC column)</p>
        <p><strong>Step 2:</strong> Click 'Merge Files' to combine the data</p>
        <p><strong>Step 3:</strong> Download your merged file</p>
        <br>
        <p><strong>💡 Tip:</strong> If you encounter encoding errors, try saving your CSV files as UTF-8 format in Excel or your spreadsheet application.</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Create two columns for file uploads
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("📁 Upload POS File")
        pos_file = st.file_uploader(
            "Choose POS CSV file",
            type=['csv'],
            key="pos_uploader",
            help="Upload your POS data CSV file containing UPC column"
        )
        if pos_file:
            st.success(f"✅ Uploaded: {pos_file.name}")
            logger.info(f"POS file uploaded: {pos_file.name}")
        st.markdown('</div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("📁 Upload Supplier File")
        supplier_file = st.file_uploader(
            "Choose Supplier CSV file",
            type=['csv'],
            key="supplier_uploader",
            help="Upload your Supplier data CSV file containing UPC column"
        )
        if supplier_file:
            st.success(f"✅ Uploaded: {supplier_file.name}")
            logger.info(f"Supplier file uploaded: {supplier_file.name}")
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Merge button (only enabled when both files are uploaded)
    st.markdown("<br>", unsafe_allow_html=True)
    
    if pos_file and supplier_file:
        col_center = st.columns([1, 2, 1])[1]
        with col_center:
            if st.button("🔄 Merge Files", use_container_width=True):
                with st.spinner("Merging data... Please wait"):
                    merged_df, success, error_msg = process_and_merge_data(pos_file, supplier_file)
                
                if success:
                    st.session_state['merged_df'] = merged_df
                    st.session_state['merge_success'] = True
                    st.markdown(
                        '<div class="success-box">✅ Success! Your files have been merged. Click below to download.</div>',
                        unsafe_allow_html=True
                    )
                else:
                    st.error(f"❌ Merge failed: {error_msg}")
    else:
        col_center = st.columns([1, 2, 1])[1]
        with col_center:
            st.button("🔄 Merge Files", disabled=True, use_container_width=True)
            if not pos_file or not supplier_file:
                st.info("Please upload both files to enable merging")
    
    # Display merged data and download option
    if st.session_state.get('merge_success', False) and 'merged_df' in st.session_state:
        merged_df = st.session_state['merged_df']
        
        st.markdown("<br>", unsafe_allow_html=True)
        st.subheader("📊 Merged Data Preview")
        st.dataframe(merged_df.head(), use_container_width=True)
        
        st.info(f"📈 Total merged records: {len(merged_df)} rows, {len(merged_df.columns)} columns")
        
        # Download button
        csv_data = convert_df_to_csv(merged_df)
        col_download = st.columns([1, 2, 1])[1]
        with col_download:
            if st.download_button(
                label="📥 Download Merged CSV",
                data=csv_data,
                file_name="Merged_POS_and_Supplier_Data.csv",
                mime="text/csv",
                use_container_width=True
            ):
                logger.info("User downloaded the merged CSV file.")

if __name__ == "__main__":
    main()
