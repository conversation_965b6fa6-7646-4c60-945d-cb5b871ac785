"""
Test script to verify fixes for critical issues in v3.1 Data Merger
"""
import pandas as pd
import polars as pl
import tempfile
import os

def test_upc_matching_with_format_differences():
    """Test UPC matching with different formats (Issue 1)"""
    print("🔧 Testing UPC Matching with Format Differences")
    print("-" * 50)
    
    try:
        # Create test data that mimics the real issue
        # POS data with shorter UPCs (some with leading zeros removed)
        pos_data = {
            'UPC': ['123456789', '234567890', '345678901', '456789012', '567890123'],
            'Product': ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'],
            'Price': [10.99, 15.50, 7.25, 12.00, 8.75]
        }
        
        # Supplier data with longer UPCs (with leading zeros)
        supplier_data = {
            'UPC': ['0123456789012', '0234567890123', '0345678901234', '9999999999999', '8888888888888'],
            'Supplier': ['Supplier X', 'Supplier Y', 'Supplier Z', 'Supplier A', 'Supplier B'],
            'Cost': [5.50, 8.25, 4.10, 6.00, 7.50]
        }
        
        pos_df = pl.DataFrame(pos_data)
        supplier_df = pl.DataFrame(supplier_data)
        
        print(f"POS UPCs: {pos_df['UPC'].to_list()}")
        print(f"Supplier UPCs: {supplier_df['UPC'].to_list()}")
        
        # Test the normalization function
        from app import normalize_upc_format, validate_and_clean_upc_column
        
        # Test normalization
        test_upcs = ['0123456789012', '123456789', '000123', '123.0', 'nan']
        normalized = [normalize_upc_format(upc) for upc in test_upcs]
        print(f"Normalization test: {test_upcs} -> {normalized}")
        
        # Test UPC validation and cleaning
        pos_df_clean = validate_and_clean_upc_column(pos_df, "test_pos")
        supplier_df_clean = validate_and_clean_upc_column(supplier_df, "test_supplier")
        
        print(f"POS normalized UPCs: {pos_df_clean['UPC_normalized'].to_list()}")
        print(f"Supplier normalized UPCs: {supplier_df_clean['UPC_normalized'].to_list()}")
        
        # Check for matches in normalized UPCs
        pos_norm_set = set(pos_df_clean['UPC_normalized'].to_list())
        supplier_norm_set = set(supplier_df_clean['UPC_normalized'].to_list())
        matches = pos_norm_set.intersection(supplier_norm_set)
        
        print(f"Normalized matches found: {len(matches)}")
        if matches:
            print(f"Matching UPCs: {list(matches)}")
            return True
        else:
            print("❌ No matches found even after normalization")
            return False
            
    except Exception as e:
        print(f"❌ UPC matching test failed: {e}")
        return False

def test_excel_hyphen_upc_fix():
    """Test Excel reading with hyphenated UPCs (Issue 2)"""
    print("\n🔧 Testing Excel Hyphen UPC Fix")
    print("-" * 50)
    
    try:
        # Create test Excel-like data with problematic UPCs
        test_data = {
            'UPC': ['111-690', '222-780', '333-890', '123456789012'],
            'Product': ['Item A', 'Item B', 'Item C', 'Item D'],
            'Price': [10.99, 15.50, 7.25, 12.00]
        }
        
        df_pandas = pd.DataFrame(test_data)
        print(f"Test data created with UPCs: {df_pandas['UPC'].tolist()}")
        
        # Test the enhanced Excel reading logic
        from app import read_file_with_polars
        
        # Create a temporary CSV file to simulate file reading
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df_pandas.to_csv(f.name, index=False)
            temp_file_path = f.name
        
        try:
            # Test file reading
            with open(temp_file_path, 'rb') as file:
                result_df = read_file_with_polars(file, 'csv')
                
            print(f"File reading successful: {result_df.shape}")
            print(f"UPC column type: {result_df['UPC'].dtype}")
            print(f"UPC values: {result_df['UPC'].to_list()}")
            
            # Verify hyphenated UPCs are preserved
            if '111-690' in result_df['UPC'].to_list():
                print("✅ Hyphenated UPCs preserved correctly")
                return True
            else:
                print("❌ Hyphenated UPCs not preserved")
                return False
                
        finally:
            # Clean up temp file
            os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"❌ Excel hyphen UPC test failed: {e}")
        return False

def test_merge_function_with_debugging():
    """Test the enhanced merge function with debugging output"""
    print("\n🔧 Testing Enhanced Merge Function")
    print("-" * 50)
    
    try:
        from app import perform_simple_merge, validate_and_clean_upc_column
        
        # Create test data that should match after normalization
        pos_data = {
            'UPC': ['123456789', '234567890', '999888777'],
            'Product': ['Product A', 'Product B', 'Product C'],
            'Price': [10.99, 15.50, 7.25]
        }
        
        supplier_data = {
            'UPC': ['0123456789012', '0234567890123', '0999888777666'],
            'Supplier': ['Supplier X', 'Supplier Y', 'Supplier Z'],
            'Cost': [5.50, 8.25, 4.10]
        }
        
        pos_df = pl.DataFrame(pos_data)
        supplier_df = pl.DataFrame(supplier_data)
        
        # Apply UPC validation and cleaning
        pos_df = validate_and_clean_upc_column(pos_df, "test_pos")
        supplier_df = validate_and_clean_upc_column(supplier_df, "test_supplier")
        
        print("Testing merge function with debugging...")
        
        # Test the merge function
        merged_df, unmatched_pos, unmatched_supplier = perform_simple_merge(pos_df, supplier_df)
        
        print(f"Merge results:")
        print(f"  Merged: {merged_df.shape[0]} rows")
        print(f"  Unmatched POS: {unmatched_pos.shape[0]} rows")
        print(f"  Unmatched Supplier: {unmatched_supplier.shape[0]} rows")
        
        if merged_df.shape[0] > 0:
            print("✅ Merge function working with enhanced debugging")
            return True
        else:
            print("⚠️ No matches found - check logs for debugging info")
            return False
            
    except Exception as e:
        print(f"❌ Merge function test failed: {e}")
        return False

def main():
    """Run all critical issue tests"""
    print("🎯 V3.1 Critical Issues Test Suite")
    print("=" * 60)
    
    tests = [
        ("UPC Matching with Format Differences", test_upc_matching_with_format_differences),
        ("Excel Hyphen UPC Fix", test_excel_hyphen_upc_fix),
        ("Enhanced Merge Function", test_merge_function_with_debugging)
    ]
    
    results = []
    for test_name, test_func in tests:
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 CRITICAL ISSUES TEST SUMMARY")
    print("=" * 60)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<35} {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL CRITICAL ISSUES RESOLVED!")
        print("✅ UPC matching format differences handled")
        print("✅ Excel hyphen UPC conversion fixed")
        print("✅ Enhanced debugging and error handling working")
    else:
        print("❌ Some critical issues remain. Check the output above.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
