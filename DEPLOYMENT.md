# 🚀 Deployment Guide

This guide will help you deploy both versions of the POS & Supplier Data Merger to GitHub and Streamlit Cloud.

## 📋 Prerequisites

- GitHub account
- Git installed on your local machine
- Streamlit Cloud account (free at [share.streamlit.io](https://share.streamlit.io/))

## 🔧 Step 1: Push to GitHub

### 1.1 Create a New Repository on GitHub

1. Go to [GitHub](https://github.com) and sign in
2. Click the "+" icon in the top right corner
3. Select "New repository"
4. Name your repository (e.g., `pos-supplier-merger`)
5. Add a description: "Professional Streamlit app for merging POS and Supplier data files"
6. Make it **Public** (required for free Streamlit Cloud deployment)
7. **Do NOT** initialize with README, .gitignore, or license (we already have these)
8. Click "Create repository"

### 1.2 Connect Local Repository to GitHub

```bash
# Add the remote origin (replace YOUR_USERNAME with your GitHub username)
git remote add origin https://github.com/YOUR_USERNAME/pos-supplier-merger.git

# Push to GitHub
git branch -M main
git push -u origin main
```

## 🌐 Step 2: Deploy to Streamlit Cloud

### 2.1 Deploy V1 (Stable - CSV Only)

1. Go to [Streamlit Cloud](https://share.streamlit.io/)
2. Sign in with your GitHub account
3. Click "New app"
4. Select your repository: `YOUR_USERNAME/pos-supplier-merger`
5. Set the following:
   - **Branch**: `main`
   - **Main file path**: `v1-stable/app.py`
   - **App URL**: `pos-supplier-merger-v1` (or your preferred name)
6. Click "Deploy!"

### 2.2 Deploy V2 (Multi-Format)

1. In Streamlit Cloud, click "New app" again
2. Select the same repository: `YOUR_USERNAME/pos-supplier-merger`
3. Set the following:
   - **Branch**: `main`
   - **Main file path**: `v2-multiformat/app.py`
   - **App URL**: `pos-supplier-merger-v2` (or your preferred name)
4. Click "Deploy!"

## 🎯 Step 3: Update Repository Links

After deployment, update your repository README with the live links:

1. Copy the Streamlit Cloud URLs for both apps
2. Edit the main `README.md` file
3. Replace the placeholder links in the "Live Deployments" section
4. Commit and push the changes:

```bash
git add README.md
git commit -m "Update deployment links"
git push
```

## 📊 Expected Deployment URLs

Your apps will be available at:
- **V1**: `https://YOUR_USERNAME-pos-supplier-merger-v1.streamlit.app/`
- **V2**: `https://YOUR_USERNAME-pos-supplier-merger-v2.streamlit.app/`

## 🔧 Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Check that `requirements.txt` is in the correct folder
   - Ensure all dependencies are listed

2. **App won't start**
   - Verify the main file path is correct (`v1-stable/app.py` or `v2-multiformat/app.py`)
   - Check the Streamlit Cloud logs for specific errors

3. **Repository not found**
   - Ensure the repository is public
   - Check that you've pushed all files to GitHub

### Streamlit Cloud Logs

- Click on your app in Streamlit Cloud
- Click "Manage app" → "Logs" to see detailed error messages

## 🔄 Making Updates

To update your deployed apps:

1. Make changes to your local files
2. Commit and push to GitHub:
   ```bash
   git add .
   git commit -m "Your update message"
   git push
   ```
3. Streamlit Cloud will automatically redeploy your apps

## 📱 Sharing Your Apps

Once deployed, you can share your apps with:
- **V1 (Simple CSV merging)**: Perfect for basic users
- **V2 (Multi-format support)**: Ideal for advanced users with diverse data sources

## 🎉 Success!

Your POS & Supplier Data Merger applications are now live and accessible to users worldwide!

### Next Steps

1. **Test both versions** with the provided sample files
2. **Share the links** with your intended users
3. **Monitor usage** through Streamlit Cloud analytics
4. **Gather feedback** and iterate on improvements

## 📞 Support

If you encounter issues:
1. Check the Streamlit Cloud documentation
2. Review the app logs in Streamlit Cloud
3. Create an issue in your GitHub repository
4. Check the individual README files in each version folder
