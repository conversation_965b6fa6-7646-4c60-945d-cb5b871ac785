# Data-Driven Label Studio V4 🏷️

**Professional Interactive Label Design Application**

A sophisticated Streamlit application that emulates professional label design software like BarTender. Features a simplified three-step workflow: Single File Upload → Visual Label Design → Batch PDF Generation.

## 🚀 Key Features

### Interactive Design Studio
- **Drag-and-Drop Canvas**: Visual label design with real-time preview
- **Dynamic Data Linking**: Connect text and barcodes to your data columns
- **Multi-Element Support**: Text, barcodes, and images on the same label
- **Professional Properties Panel**: Font selection, sizing, colors, and positioning

### Simplified Data Processing
- **Single File Upload**: No complex merging - just upload your data file
- **Multi-Format Support**: CSV, Excel, TSV, and TXT files
- **Smart Data Loading**: Automatic format detection and error handling

### Professional Output
- **High-Quality PDF Generation**: Precise positioning using fpdf2
- **Batch Processing**: Generate hundreds of labels in one operation
- **Flexible Layout**: Configurable labels per page (rows × columns)
- **Multiple Page Sizes**: A4 and Letter support

### Barcode Integration
- **Automatic Generation**: Code128 barcodes from UPC data
- **Customizable Display**: Show/hide text below barcodes
- **High Resolution**: Scalable barcode images for print quality

## 🛠️ Installation

1. **Clone or download** this folder to your local machine
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run the application**:
   ```bash
   streamlit run app.py
   ```

## 📋 Requirements

- Python 3.8+
- Streamlit 1.28.0+
- Polars 0.20.0+
- streamlit-drawable-canvas 0.9.0+
- python-barcode 0.15.0+
- fpdf2 2.7.0+
- Additional dependencies listed in `requirements.txt`

## 🎯 Three-Step Workflow

### Step 1: Select Data File
1. **Upload File**: Select your data file (CSV, Excel, TSV, or TXT)
2. **Automatic Loading**: File is processed and validated
3. **Data Preview**: Review the first 10 rows of your data
4. **Column Detection**: Available fields are automatically identified

### Step 2: Visual Label Design Studio
1. **Canvas Setup**: Define label dimensions in millimeters
2. **Add Elements**: Use toolbox to add text, barcodes, and images
3. **Dynamic Linking**: Connect elements to data columns
4. **Properties Panel**: Customize fonts, colors, and positioning
5. **Real-Time Preview**: See changes instantly on the canvas

### Step 3: Generate & Download PDF
1. **Print Settings**: Configure page size and label layout
2. **Batch Generation**: Process all data rows automatically
3. **Professional Output**: Download high-quality PDF for printing

## 🎨 Design Elements

### Text Elements
- **Dynamic Content**: Link to any data column
- **Font Control**: Size, family, bold, italic
- **Color Customization**: Full color picker support
- **Positioning**: Precise X/Y coordinates and dimensions

### Barcode Elements
- **Code128 Standard**: Industry-standard barcode format
- **UPC Integration**: Automatic linking to UPC column
- **Text Display**: Optional text below barcode
- **Scalable Output**: High-resolution for print quality

### Image Elements
- **File Upload**: PNG, JPG, JPEG, GIF support
- **Aspect Ratio**: Maintain proportions option
- **Base64 Storage**: Embedded in design for portability

## 📊 Data Requirements

### Input Files
- **POS Data**: Must contain UPC column
- **Supplier Data**: Must contain UPC column for merging
- **Supported Formats**: CSV, Excel (.xlsx, .xls), TSV, TXT

### Data Processing
- **UPC Conversion**: Automatic string conversion for accurate merging
- **Inner Join**: Only matching UPCs are included in final dataset
- **Column Preservation**: All columns from both files are retained

## 🖨️ Output Specifications

### PDF Generation
- **Page Sizes**: A4 (210×297mm), Letter (216×279mm)
- **Layout Options**: 1-10 labels per row, 1-20 labels per column
- **Margins**: 10mm standard margins
- **Quality**: Vector-based text and scalable barcodes

### Label Dimensions
- **Flexible Sizing**: 10-300mm width/height
- **Millimeter Precision**: Professional measurement units
- **Canvas Scaling**: Automatic scaling for screen display

## 🔧 Technical Architecture

### Frontend
- **Streamlit Framework**: Modern web interface
- **Custom CSS**: Professional styling and layout
- **Interactive Canvas**: streamlit-drawable-canvas integration

### Data Processing
- **Polars Engine**: High-performance data manipulation
- **Pandas Compatibility**: Seamless conversion when needed
- **Error Handling**: Robust file reading with fallbacks

### PDF Generation
- **fpdf2 Library**: Precise positioning and layout
- **Element Rendering**: Custom rendering for each element type
- **Batch Processing**: Efficient handling of large datasets

## 📝 Usage Tips

1. **File Preparation**: Ensure both files have UPC columns with matching formats
2. **Design Strategy**: Start with text elements, then add barcodes and images
3. **Data Linking**: Use the dropdown to connect elements to data columns
4. **Preview Testing**: Generate a small preview before processing all data
5. **Print Settings**: Test with different layouts to optimize page usage

## 🆕 Version 4.0 Improvements

- **Interactive Canvas**: Complete visual design interface
- **Dynamic Data Linking**: Real-time connection to data columns
- **Professional Output**: fpdf2-based PDF generation
- **Enhanced UI**: Modern, intuitive interface design
- **Barcode Integration**: Automatic Code128 generation
- **Multi-Element Support**: Text, barcodes, and images together

## 🤝 Support

For issues or questions:
1. Check the application logs (`label_studio.log`)
2. Verify file formats and UPC column presence
3. Ensure all dependencies are installed correctly

## 📄 License

This application is part of the Data Merger project series and follows the same licensing terms.

---

**Data-Driven Label Studio V4** - Professional label design made simple! 🏷️✨
