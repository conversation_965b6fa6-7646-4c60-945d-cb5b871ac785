"""
Test script to verify V3.1 functionality including UPC column preservation
"""
import polars as pl
from io import StringIO

def test_polars_functionality():
    """Test basic Polars functionality"""
    print("Testing Polars functionality...")
    
    # Create test data
    pos_data = {
        'UPC': ['123', '456', '789'],
        'Product': ['A', 'B', 'C'],
        'Price': [10.0, 20.0, 30.0]
    }
    
    supplier_data = {
        'UPC': ['123', '456', '999'],
        'Supplier': ['X', 'Y', 'Z'],
        'Cost': [5.0, 10.0, 15.0]
    }
    
    # Create Polars DataFrames
    pos_df = pl.DataFrame(pos_data)
    supplier_df = pl.DataFrame(supplier_data)
    
    print(f"POS DataFrame: {pos_df.shape}")
    print(f"Supplier DataFrame: {supplier_df.shape}")
    
    # Test different merge types
    print("\nTesting Inner Join:")
    inner_result = pos_df.join(supplier_df, on="UPC", how="inner")
    print(f"Inner join result: {inner_result.shape[0]} rows")
    print(inner_result)
    
    print("\nTesting Left Join:")
    left_result = pos_df.join(supplier_df, on="UPC", how="left")
    print(f"Left join result: {left_result.shape[0]} rows")
    print(left_result)
    
    print("\nTesting Outer Join:")
    outer_result = pos_df.join(supplier_df, on="UPC", how="outer")
    print(f"Outer join result: {outer_result.shape[0]} rows")
    print(outer_result)
    
    print("\n✅ All Polars tests passed!")

def test_file_reading():
    """Test file reading functionality"""
    print("\nTesting file reading...")
    
    # Create test CSV content
    csv_content = """UPC,Product,Price
123,Widget A,10.99
456,Widget B,15.50
789,Widget C,7.25"""
    
    # Test CSV reading
    try:
        df = pl.read_csv(StringIO(csv_content))
        print(f"✅ CSV reading successful: {df.shape}")
        print(df)
    except Exception as e:
        print(f"❌ CSV reading failed: {e}")
    
    print("\n✅ File reading tests completed!")

def test_upc_column_preservation():
    """Test that UPC column is always preserved in merges"""
    print("\nTesting UPC column preservation...")

    # Create test data with various column names
    pos_data = {
        'UPC': ['123', '456', '789'],
        'Product_Name': ['Widget A', 'Widget B', 'Widget C'],
        'Store_ID': ['001', '002', '003'],
        'Unit_Price': [10.99, 15.50, 7.25]
    }

    supplier_data = {
        'UPC': ['123', '456', '999'],
        'Supplier_Name': ['ABC Corp', 'XYZ Inc', 'DEF Ltd'],
        'Cost_Price': [5.50, 8.25, 4.10],
        'Category': ['Electronics', 'Home', 'Sports']
    }

    # Create DataFrames
    pos_df = pl.DataFrame(pos_data)
    supplier_df = pl.DataFrame(supplier_data)

    print(f"Original POS columns: {pos_df.columns}")
    print(f"Original Supplier columns: {supplier_df.columns}")

    # Test inner join (main merge operation)
    merged_df = pos_df.join(supplier_df, on="UPC", how="inner")

    print(f"\nMerged DataFrame columns: {merged_df.columns}")
    print(f"Merged DataFrame shape: {merged_df.shape}")

    # Verify UPC column exists
    assert "UPC" in merged_df.columns, "❌ UPC column missing from merged data!"
    print("✅ UPC column preserved in merge")

    # Test column selection simulation (what happens in the app)
    available_columns = merged_df.columns
    non_upc_columns = [col for col in available_columns if col != "UPC"]

    # Simulate user selecting all non-UPC columns
    selected_columns = ["UPC"] + non_upc_columns  # UPC always included
    working_df = merged_df.select(selected_columns)

    assert "UPC" in working_df.columns, "❌ UPC column missing after column selection!"
    print("✅ UPC column preserved in column selection")

    # Verify UPC values are intact
    original_upcs = set(merged_df["UPC"].to_list())
    final_upcs = set(working_df["UPC"].to_list())
    assert original_upcs == final_upcs, "❌ UPC values changed during processing!"
    print("✅ UPC values preserved correctly")

    print(f"Final DataFrame with UPC protection: {working_df.shape}")
    print(working_df)

    print("\n✅ UPC column preservation tests passed!")

def test_large_upc_handling():
    """Test handling of large UPC numbers that might cause 'int too big to convert' errors"""
    print("\nTesting large UPC number handling...")

    # Create test data with very large UPC numbers (typical 12-13 digit UPCs)
    large_upcs = ['123456789012', '999999999999', '1234567890123']

    pos_data = {
        'UPC': large_upcs,
        'Product': ['Product A', 'Product B', 'Product C'],
        'Price': [10.99, 15.50, 7.25]
    }

    supplier_data = {
        'UPC': large_upcs[:2] + ['888888888888'],  # First two match, third doesn't
        'Supplier': ['Supplier X', 'Supplier Y', 'Supplier Z'],
        'Cost': [5.50, 8.25, 4.10]
    }

    # Create DataFrames with string UPC columns
    pos_df = pl.DataFrame(pos_data)
    supplier_df = pl.DataFrame(supplier_data)

    # Ensure UPC columns are strings
    pos_df = pos_df.with_columns(pl.col("UPC").cast(pl.Utf8))
    supplier_df = supplier_df.with_columns(pl.col("UPC").cast(pl.Utf8))

    print(f"POS UPCs: {pos_df['UPC'].to_list()}")
    print(f"Supplier UPCs: {supplier_df['UPC'].to_list()}")

    # Test merge with large UPCs
    merged_df = pos_df.join(supplier_df, on="UPC", how="inner")

    print(f"Merged DataFrame shape: {merged_df.shape}")
    print(f"Merged UPCs: {merged_df['UPC'].to_list()}")

    # Verify UPC column exists and contains correct values
    assert "UPC" in merged_df.columns, "❌ UPC column missing from merged data!"
    assert merged_df.shape[0] == 2, f"❌ Expected 2 merged rows, got {merged_df.shape[0]}"

    merged_upcs = set(merged_df["UPC"].to_list())
    expected_upcs = {'123456789012', '999999999999'}
    assert merged_upcs == expected_upcs, f"❌ UPC values incorrect. Expected {expected_upcs}, got {merged_upcs}"

    print("✅ Large UPC numbers handled correctly")
    print("✅ UPC column preserved with large numbers")
    print("✅ Merge operations work with 12+ digit UPCs")

    print("\n✅ Large UPC handling tests passed!")

def test_upc_validation_and_cleaning():
    """Test the UPC validation and cleaning functionality"""
    print("\nTesting UPC validation and cleaning...")

    # Import the validation function
    from app import validate_and_clean_upc_column

    # Create test data with various UPC issues
    test_data = {
        'UPC': ['123456789012', '999999999999.0', 'nan', '  456789012345  ', 'None', ''],
        'Product': ['Product A', 'Product B', 'Product C', 'Product D', 'Product E', 'Product F'],
        'Price': [10.99, 15.50, 7.25, 12.00, 8.75, 5.50]
    }

    df = pl.DataFrame(test_data)
    print(f"Original UPCs: {df['UPC'].to_list()}")

    # Apply validation and cleaning
    cleaned_df = validate_and_clean_upc_column(df, "test_file")
    cleaned_upcs = cleaned_df['UPC'].to_list()

    print(f"Cleaned UPCs: {cleaned_upcs}")

    # Verify cleaning worked correctly
    expected_upcs = ['123456789012', '999999999999', '', '456789012345', '', '']
    assert cleaned_upcs == expected_upcs, f"UPC cleaning failed. Expected {expected_upcs}, got {cleaned_upcs}"

    print("✅ UPC validation and cleaning working correctly")
    print("✅ Trailing .0 removed from UPCs")
    print("✅ 'nan' and 'None' values cleaned")
    print("✅ Whitespace trimmed from UPCs")

    print("\n✅ UPC validation and cleaning tests passed!")

def test_excel_reading_robustness():
    """Test Excel reading with various scenarios"""
    print("\nTesting Excel reading robustness...")

    # Test that the Excel reading function exists and can be imported
    try:
        from app import read_file_with_polars
        print("✅ Excel reading function imported successfully")
        # Test that the function is callable
        assert callable(read_file_with_polars), "read_file_with_polars should be callable"
    except ImportError as e:
        print(f"❌ Failed to import Excel reading function: {e}")
        return

    # Test that pandas Excel reading with UPC dtype works
    try:
        import pandas as pd

        # Create a simple test Excel-like data structure
        test_data = pd.DataFrame({
            'UPC': [123456789012, 999999999999, 1234567890123],
            'Product': ['Test A', 'Test B', 'Test C'],
            'Price': [10.99, 15.50, 7.25]
        })

        # Test that we can convert large UPCs to string
        test_data['UPC'] = test_data['UPC'].astype(str)
        upcs = test_data['UPC'].tolist()

        print(f"✅ Large UPC conversion test: {upcs}")
        assert all(isinstance(upc, str) for upc in upcs), "UPCs should be strings"
        assert all(len(upc) >= 12 for upc in upcs), "UPCs should be 12+ digits"

        print("✅ Excel UPC handling logic verified")

    except Exception as e:
        print(f"⚠️ Excel reading test warning: {e}")

    print("\n✅ Excel reading robustness tests completed!")

if __name__ == "__main__":
    test_polars_functionality()
    test_file_reading()
    test_upc_column_preservation()
    test_large_upc_handling()
    test_upc_validation_and_cleaning()
    test_excel_reading_robustness()
    print("\n🎉 All tests completed!")
