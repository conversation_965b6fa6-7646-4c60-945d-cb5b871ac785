#!/usr/bin/env python3
"""
Comprehensive Test Suite for Data-Driven Label Studio V4
Tests all core functionality including imports, data processing, label design, and PDF generation.
"""

import sys
import os
import traceback
from io import BytesIO
import tempfile

def test_imports():
    """Test all required imports."""
    print("🔍 Testing imports...")
    try:
        import streamlit as st
        import polars as pl
        import pandas as pd
        import openpyxl
        import streamlit_drawable_canvas
        import barcode
        import fpdf
        import PIL
        import qrcode
        print("✅ All dependencies imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_app_imports():
    """Test app module imports."""
    print("🔍 Testing app module imports...")
    try:
        from app import (
            TextElement, BarcodeElement, ImageElement, ShapeElement, LineElement,
            detect_file_format, read_file_with_polars, load_data_file,
            generate_barcode_image, render_label_elements_to_image,
            generate_labels_pdf, initialize_session_state
        )
        print("✅ App module imports successful")
        return True
    except ImportError as e:
        print(f"❌ App import error: {e}")
        traceback.print_exc()
        return False

def test_element_creation():
    """Test label element creation."""
    print("🔍 Testing element creation...")
    try:
        from app import TextElement, BarcodeElement, ImageElement, ShapeElement, LineElement
        
        # Test element creation
        text_elem = TextElement()
        barcode_elem = BarcodeElement()
        image_elem = ImageElement()
        shape_elem = ShapeElement()
        line_elem = LineElement()
        
        # Test element properties
        assert text_elem.element_type == 'text'
        assert barcode_elem.element_type == 'barcode'
        assert image_elem.element_type == 'image'
        assert shape_elem.element_type == 'shape'
        assert line_elem.element_type == 'line'
        
        # Test serialization
        text_dict = text_elem.to_dict()
        restored_elem = TextElement.from_dict(text_dict)
        assert restored_elem.element_type == text_elem.element_type
        
        print("✅ Element creation and serialization working")
        return True
    except Exception as e:
        print(f"❌ Element creation error: {e}")
        traceback.print_exc()
        return False

def test_file_format_detection():
    """Test file format detection."""
    print("🔍 Testing file format detection...")
    try:
        from app import detect_file_format
        
        class MockFile:
            def __init__(self, name):
                self.name = name
        
        # Test different formats
        csv_format = detect_file_format(MockFile('test.csv'))
        excel_format = detect_file_format(MockFile('test.xlsx'))
        tsv_format = detect_file_format(MockFile('test.tsv'))
        txt_format = detect_file_format(MockFile('test.txt'))
        unknown_format = detect_file_format(MockFile('test.unknown'))
        
        assert csv_format == 'csv'
        assert excel_format == 'excel'
        assert tsv_format == 'tsv'
        assert txt_format == 'txt'
        assert unknown_format == 'unknown'
        
        print("✅ File format detection working")
        return True
    except Exception as e:
        print(f"❌ File format detection error: {e}")
        traceback.print_exc()
        return False

def test_barcode_generation():
    """Test barcode generation."""
    print("🔍 Testing barcode generation...")
    try:
        from app import generate_barcode_image
        
        # Test barcode generation
        barcode_img = generate_barcode_image('123456789')
        assert barcode_img is not None
        assert hasattr(barcode_img, 'size')  # PIL Image has size attribute
        
        print("✅ Barcode generation working")
        return True
    except Exception as e:
        print(f"❌ Barcode generation error: {e}")
        traceback.print_exc()
        return False

def test_label_rendering():
    """Test label rendering."""
    print("🔍 Testing label rendering...")
    try:
        from app import render_label_elements_to_image, TextElement, BarcodeElement
        
        # Create test elements
        text_elem = TextElement()
        text_elem.properties['text'] = 'Test Label'
        
        barcode_elem = BarcodeElement()
        barcode_elem.data_column = 'UPC'
        
        elements = [text_elem, barcode_elem]
        
        # Test rendering
        rendered_img = render_label_elements_to_image(600, 300, elements)
        assert rendered_img is not None
        assert hasattr(rendered_img, 'size')
        assert rendered_img.size == (600, 300)
        
        print("✅ Label rendering working")
        return True
    except Exception as e:
        print(f"❌ Label rendering error: {e}")
        traceback.print_exc()
        return False

def test_pdf_generation():
    """Test PDF generation."""
    print("🔍 Testing PDF generation...")
    try:
        from app import generate_labels_pdf, TextElement, BarcodeElement
        import polars as pl
        
        # Create test data
        test_data = pl.DataFrame({
            'UPC': ['123456789', '987654321'],
            'Item Name': ['Test Item 1', 'Test Item 2'],
            'Price': [1.99, 2.99]
        })
        
        # Create test elements
        text_elem = TextElement()
        text_elem.data_column = 'Item Name'
        
        barcode_elem = BarcodeElement()
        barcode_elem.data_column = 'UPC'
        
        elements = [text_elem, barcode_elem]
        
        # Test PDF generation
        pdf_buffer = generate_labels_pdf(
            test_data, 
            elements, 
            100,  # width_mm
            50,   # height_mm
            'A4', # page_size
            2,    # labels_per_row
            5     # labels_per_column
        )
        
        assert pdf_buffer is not None
        pdf_bytes = pdf_buffer.getvalue()
        assert len(pdf_bytes) > 1000  # PDF should be reasonably sized
        assert pdf_bytes.startswith(b'%PDF')  # PDF header
        
        print(f"✅ PDF generation working (size: {len(pdf_bytes)} bytes)")
        return True
    except Exception as e:
        print(f"❌ PDF generation error: {e}")
        traceback.print_exc()
        return False

def test_data_processing():
    """Test data processing functionality."""
    print("🔍 Testing data processing...")
    try:
        import polars as pl
        import pandas as pd
        from app import load_data_file
        
        # Create test CSV data
        test_csv_content = """UPC,Item Name,Price
123456789,Test Item 1,1.99
987654321,Test Item 2,2.99"""
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(test_csv_content)
            temp_file_path = f.name
        
        try:
            # Test file loading (simulate file upload)
            class MockFile:
                def __init__(self, path):
                    self.name = os.path.basename(path)
                    self._path = path
                    self._pos = 0
                
                def read(self):
                    with open(self._path, 'rb') as f:
                        return f.read()
                
                def seek(self, pos):
                    self._pos = pos
            
            mock_file = MockFile(temp_file_path)
            df = load_data_file(mock_file)
            
            assert df is not None
            assert len(df) == 2
            assert 'UPC' in df.columns
            assert 'Item Name' in df.columns
            assert 'Price' in df.columns
            
            print("✅ Data processing working")
            return True
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"❌ Data processing error: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all tests and return summary."""
    print("🚀 Starting V4 Label Studio Comprehensive Test Suite")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_imports),
        ("App Imports", test_app_imports),
        ("Element Creation", test_element_creation),
        ("File Format Detection", test_file_format_detection),
        ("Barcode Generation", test_barcode_generation),
        ("Label Rendering", test_label_rendering),
        ("PDF Generation", test_pdf_generation),
        ("Data Processing", test_data_processing),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! V4 Label Studio is ready for use.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
