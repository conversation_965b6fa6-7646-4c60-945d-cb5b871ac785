# POS & Supplier Data Merger

A beautiful, interactive Streamlit web application for merging POS (Point of Sale) and Supplier CSV files based on UPC (Universal Product Code) columns.

## Features

- 🎨 **Modern, Professional UI**: Clean design with custom CSS styling
- 📊 **Smart Data Merging**: Handles data type mismatches in UPC columns automatically
- 📝 **Comprehensive Logging**: Tracks all operations and errors in `app.log`
- 🔄 **Real-time Feedback**: Progress indicators and status updates
- 📥 **Easy Download**: One-click download of merged data
- 🛡️ **Error Handling**: Robust error handling with detailed logging

## Requirements

- Python 3.7+
- Streamlit 1.28.0+
- Pandas 2.0.0+

## Installation

1. **Clone or download this repository**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. **Start the application**:
   ```bash
   streamlit run app.py
   ```

2. **Open your browser** and navigate to the URL shown in the terminal (usually `http://localhost:8501`)

3. **Upload your files**:
   - Upload your POS CSV file (must contain a 'UPC' column)
   - Upload your Supplier CSV file (must contain a 'UPC' column)

4. **Merge the data**:
   - Click the "Merge Files" button
   - Wait for the processing to complete

5. **Download results**:
   - Preview the merged data
   - Click "Download Merged CSV" to save the file as `Merged_POS_and_Supplier_Data.csv`

## File Requirements

Both CSV files must contain a column named **'UPC'**. The application automatically:
- Converts UPC columns to string type to handle data type mismatches
- Performs an inner join (only includes rows where UPC exists in both files)
- Preserves all columns from both original files

## Logging

The application creates an `app.log` file that tracks:
- Application sessions
- File uploads
- Merge operations
- Download events
- Error messages

## Technical Details

### Data Processing
- **UPC Conversion**: Both UPC columns are converted to string type before merging
- **Merge Type**: Inner join on UPC column
- **Column Handling**: All columns from both files are preserved
- **Duplicate Columns**: Pandas automatically handles duplicate column names with suffixes

### Error Handling
- File validation (CSV format, UPC column presence)
- Data type conversion errors
- Memory and processing errors
- Comprehensive error logging

## Troubleshooting

### Common Issues

1. **"UPC column not found"**
   - Ensure both CSV files have a column named exactly 'UPC'
   - Check for extra spaces or different capitalization

2. **"No data after merge"**
   - Verify that there are matching UPC values between the two files
   - Check the UPC format consistency

3. **Memory errors with large files**
   - Try processing smaller file chunks
   - Ensure sufficient system memory

### Log File
Check the `app.log` file for detailed error messages and processing information.

## File Structure

```
Matcher/
├── app.py              # Main Streamlit application
├── requirements.txt    # Python dependencies
├── README.md          # This file
└── app.log            # Generated log file (created when app runs)
```

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this application.

## License

This project is open source and available under the MIT License.
