#!/usr/bin/env python3
"""
Error Check Script for V4 Label Studio
Identifies and reports any remaining issues in the application.
"""

import sys
import traceback
import warnings

def check_imports():
    """Check all required imports."""
    print("🔍 Checking imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported")
        
        import polars as pl
        print("✅ Polars imported")
        
        import pandas as pd
        print("✅ Pandas imported")
        
        from streamlit_drawable_canvas import st_canvas
        print("✅ Drawable canvas imported")
        
        import barcode
        print("✅ Barcode library imported")
        
        from fpdf import FPDF
        print("✅ FPDF imported")
        
        from PIL import Image, ImageDraw, ImageFont
        print("✅ PIL imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def check_app_functions():
    """Check core app functions."""
    print("\n🔍 Checking app functions...")
    
    try:
        # Import app module
        import app
        print("✅ App module imported")
        
        # Test element creation
        text_elem = app.TextElement()
        barcode_elem = app.BarcodeElement()
        print("✅ Element creation working")
        
        # Test barcode generation with edge cases
        test_cases = [
            ("normal", "123456789012"),
            ("empty", ""),
            ("none", None),
            ("short", "12"),
            ("special_chars", "123-456-789"),
        ]
        
        for case_name, test_input in test_cases:
            try:
                result = app.generate_barcode_image(test_input)
                status = "✅" if result is not None else "⚠️"
                print(f"{status} Barcode {case_name}: {result is not None}")
            except Exception as e:
                print(f"❌ Barcode {case_name} failed: {e}")
        
        # Test label rendering
        elements = [text_elem, barcode_elem]
        rendered_img = app.render_label_elements_to_image(600, 300, elements)
        print(f"✅ Label rendering: {rendered_img is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ App function error: {e}")
        traceback.print_exc()
        return False

def check_pdf_generation():
    """Check PDF generation."""
    print("\n🔍 Checking PDF generation...")
    
    try:
        import app
        import polars as pl
        
        # Create test data
        test_data = pl.DataFrame({
            'UPC': ['123456789012', '987654321098'],
            'Item Name': ['Test Item 1', 'Test Item 2'],
            'Price': [1.99, 2.99]
        })
        
        # Create elements
        text_elem = app.TextElement()
        text_elem.data_column = 'Item Name'
        
        barcode_elem = app.BarcodeElement()
        barcode_elem.data_column = 'UPC'
        
        elements = [text_elem, barcode_elem]
        
        # Test PDF generation
        pdf_buffer = app.generate_labels_pdf(
            test_data, elements, 100, 50, 'A4', 2, 2
        )
        
        if pdf_buffer:
            pdf_bytes = pdf_buffer.getvalue()
            print(f"✅ PDF generation: {len(pdf_bytes)} bytes")
            return True
        else:
            print("❌ PDF generation returned None")
            return False
            
    except Exception as e:
        print(f"❌ PDF generation error: {e}")
        traceback.print_exc()
        return False

def check_streamlit_app():
    """Check if Streamlit app is accessible."""
    print("\n🔍 Checking Streamlit app...")
    
    try:
        import requests
        response = requests.get('http://localhost:8501', timeout=5)
        
        if response.status_code == 200:
            print(f"✅ Streamlit app accessible (status: {response.status_code})")
            print(f"   Response size: {len(response.content)} bytes")
            
            # Check for error indicators in response
            content = response.text.lower()
            error_indicators = ['error', 'exception', 'traceback', 'failed']
            
            for indicator in error_indicators:
                if indicator in content:
                    print(f"⚠️ Found '{indicator}' in response")
            
            return True
        else:
            print(f"❌ Streamlit app returned status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Streamlit app check failed: {e}")
        return False

def check_data_processing():
    """Check data processing functionality."""
    print("\n🔍 Checking data processing...")
    
    try:
        import app
        import tempfile
        import os
        
        # Create test CSV
        test_csv = """UPC,Item Name,Price
123456789012,Test Item 1,1.99
987654321098,Test Item 2,2.99"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(test_csv)
            temp_path = f.name
        
        try:
            # Test file format detection
            class MockFile:
                def __init__(self, name):
                    self.name = name
                def seek(self, pos):
                    pass
                def read(self):
                    with open(temp_path, 'rb') as f:
                        return f.read()
            
            mock_file = MockFile(os.path.basename(temp_path))
            file_format = app.detect_file_format(mock_file)
            print(f"✅ File format detection: {file_format}")
            
            # Test data loading
            df = app.load_data_file(mock_file)
            print(f"✅ Data loading: {len(df)} rows")
            
            return True
            
        finally:
            os.unlink(temp_path)
            
    except Exception as e:
        print(f"❌ Data processing error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all error checks."""
    print("🚀 V4 Label Studio Error Check")
    print("=" * 50)
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings('ignore')
    
    checks = [
        ("Imports", check_imports),
        ("App Functions", check_app_functions),
        ("PDF Generation", check_pdf_generation),
        ("Data Processing", check_data_processing),
        ("Streamlit App", check_streamlit_app),
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} check failed: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 ERROR CHECK SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {check_name}")
    
    print(f"\n🎯 Result: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 ALL CHECKS PASSED! No errors found.")
    else:
        print("⚠️ Some checks failed. Review the output above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
