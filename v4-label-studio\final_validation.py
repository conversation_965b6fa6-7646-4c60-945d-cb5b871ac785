#!/usr/bin/env python3
"""
Final Validation Script for Data-Driven Label Studio V4
Performs comprehensive testing of all functionality to ensure everything works perfectly.
"""

import sys
import os
import traceback
from io import BytesIO
import tempfile
import time

def test_streamlit_imports():
    """Test Streamlit and all dependencies."""
    print("🔍 Testing Streamlit and dependencies...")
    try:
        import streamlit as st
        import polars as pl
        import pandas as pd
        import openpyxl
        import streamlit_drawable_canvas
        import barcode
        import fpdf
        import PIL
        import qrcode
        print("✅ All Streamlit dependencies working")
        return True
    except Exception as e:
        print(f"❌ Streamlit dependency error: {e}")
        return False

def test_app_functionality():
    """Test core app functionality."""
    print("🔍 Testing app functionality...")
    try:
        from app import (
            TextElement, BarcodeElement, ImageElement, ShapeElement, LineElement,
            detect_file_format, generate_barcode_image, render_label_elements_to_image,
            generate_labels_pdf, initialize_session_state
        )
        
        # Test element creation
        text_elem = TextElement()
        barcode_elem = BarcodeElement()
        image_elem = ImageElement()
        shape_elem = ShapeElement()
        line_elem = LineElement()
        
        # Test properties
        text_elem.properties['text'] = 'Test Label'
        text_elem.data_column = 'Item Name'
        
        barcode_elem.data_column = 'UPC'
        
        print("✅ Element creation working")
        
        # Test barcode generation
        barcode_img = generate_barcode_image('123456789')
        if barcode_img and hasattr(barcode_img, 'size'):
            print("✅ Barcode generation working")
        else:
            print("⚠️ Barcode generation issue")
            return False
            
        # Test label rendering
        elements = [text_elem, barcode_elem]
        rendered_img = render_label_elements_to_image(600, 300, elements)
        if rendered_img and hasattr(rendered_img, 'size'):
            print("✅ Label rendering working")
        else:
            print("⚠️ Label rendering issue")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ App functionality error: {e}")
        traceback.print_exc()
        return False

def test_pdf_generation():
    """Test PDF generation with real data."""
    print("🔍 Testing PDF generation...")
    try:
        from app import generate_labels_pdf, TextElement, BarcodeElement
        import polars as pl
        
        # Create comprehensive test data
        test_data = pl.DataFrame({
            'UPC': ['123456789012', '987654321098', '456789012345'],
            'Item Name': ['Premium Coffee Beans', 'Organic Tea Leaves', 'Artisan Chocolate'],
            'Price': [12.99, 8.49, 15.99],
            'Category': ['Beverages', 'Beverages', 'Confectionery']
        })
        
        # Create multiple elements
        text_elem1 = TextElement(x=10, y=10, width=180, height=20)
        text_elem1.data_column = 'Item Name'
        text_elem1.properties['font_size'] = 14
        text_elem1.properties['bold'] = True
        
        text_elem2 = TextElement(x=10, y=35, width=80, height=15)
        text_elem2.data_column = 'Price'
        text_elem2.properties['font_size'] = 12
        
        barcode_elem = BarcodeElement(x=200, y=10, width=100, height=40)
        barcode_elem.data_column = 'UPC'
        
        elements = [text_elem1, text_elem2, barcode_elem]
        
        # Test PDF generation
        pdf_buffer = generate_labels_pdf(
            test_data, 
            elements, 
            100,  # width_mm
            60,   # height_mm
            'A4', # page_size
            2,    # labels_per_row
            4     # labels_per_column
        )
        
        if pdf_buffer:
            pdf_bytes = pdf_buffer.getvalue()
            if len(pdf_bytes) > 2000 and pdf_bytes.startswith(b'%PDF'):
                print(f"✅ PDF generation working (size: {len(pdf_bytes)} bytes)")
                
                # Save test PDF
                with open('test_labels.pdf', 'wb') as f:
                    f.write(pdf_bytes)
                print("✅ Test PDF saved as 'test_labels.pdf'")
                return True
            else:
                print("⚠️ PDF generation produced invalid output")
                return False
        else:
            print("⚠️ PDF generation returned None")
            return False
            
    except Exception as e:
        print(f"❌ PDF generation error: {e}")
        traceback.print_exc()
        return False

def test_data_processing():
    """Test data file processing."""
    print("🔍 Testing data processing...")
    try:
        from app import load_data_file, detect_file_format
        import polars as pl
        
        # Create test CSV content
        test_csv_content = """UPC,Item Name,Price,Category
123456789012,Premium Coffee Beans,12.99,Beverages
987654321098,Organic Tea Leaves,8.49,Beverages
456789012345,Artisan Chocolate,15.99,Confectionery
789012345678,Gourmet Cookies,6.99,Bakery"""
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(test_csv_content)
            temp_file_path = f.name
        
        try:
            # Test file format detection
            class MockFile:
                def __init__(self, path):
                    self.name = os.path.basename(path)
                    self._path = path
                
                def seek(self, pos):
                    pass
                
                def read(self):
                    with open(self._path, 'rb') as f:
                        return f.read()
            
            mock_file = MockFile(temp_file_path)
            file_format = detect_file_format(mock_file)
            
            if file_format == 'csv':
                print("✅ File format detection working")
            else:
                print(f"⚠️ File format detection issue: got {file_format}")
                return False
            
            # Test data loading
            df = load_data_file(mock_file)
            
            if df is not None and len(df) == 4:
                print("✅ Data loading working")
                print(f"   Loaded {len(df)} rows with columns: {list(df.columns)}")
                return True
            else:
                print("⚠️ Data loading issue")
                return False
                
        finally:
            # Clean up
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
            
    except Exception as e:
        print(f"❌ Data processing error: {e}")
        traceback.print_exc()
        return False

def test_error_handling():
    """Test error handling and edge cases."""
    print("🔍 Testing error handling...")
    try:
        from app import generate_barcode_image, render_label_elements_to_image, TextElement
        
        # Test with invalid barcode data
        barcode_img = generate_barcode_image('')
        if barcode_img is None:
            print("✅ Empty barcode handling working")
        
        # Test with empty elements list
        rendered_img = render_label_elements_to_image(600, 300, [])
        if rendered_img and hasattr(rendered_img, 'size'):
            print("✅ Empty elements handling working")
        
        # Test with invalid element properties
        text_elem = TextElement()
        text_elem.properties = {}  # Empty properties
        elements = [text_elem]
        rendered_img = render_label_elements_to_image(600, 300, elements)
        if rendered_img:
            print("✅ Invalid properties handling working")
        
        return True
        
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def check_streamlit_app():
    """Check if Streamlit app is accessible."""
    print("🔍 Checking Streamlit app accessibility...")
    try:
        import requests
        response = requests.get('http://localhost:8501', timeout=5)
        if response.status_code == 200:
            print("✅ Streamlit app is accessible")
            return True
        else:
            print(f"⚠️ Streamlit app returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠️ Could not access Streamlit app: {e}")
        return False

def run_final_validation():
    """Run all validation tests."""
    print("🚀 FINAL VALIDATION - Data-Driven Label Studio V4")
    print("=" * 70)
    
    tests = [
        ("Streamlit Dependencies", test_streamlit_imports),
        ("App Functionality", test_app_functionality),
        ("PDF Generation", test_pdf_generation),
        ("Data Processing", test_data_processing),
        ("Error Handling", test_error_handling),
        ("Streamlit App Access", check_streamlit_app),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Final Summary
    print("\n" + "=" * 70)
    print("📊 FINAL VALIDATION SUMMARY")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 VALIDATION COMPLETE! V4 Label Studio is fully functional and ready for production use!")
        print("\n📋 What's working:")
        print("   • All dependencies properly installed")
        print("   • Label design elements (text, barcodes, images, shapes)")
        print("   • Interactive canvas rendering")
        print("   • PDF generation with multiple layouts")
        print("   • Data file processing (CSV, Excel, TSV, TXT)")
        print("   • Error handling and edge cases")
        print("   • Streamlit web interface")
        print("\n🌐 Access your application at: http://localhost:8501")
        return True
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = run_final_validation()
    sys.exit(0 if success else 1)
