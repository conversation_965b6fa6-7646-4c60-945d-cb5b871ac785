"""
Label Creator V3.5 - Professional Label Generation Application
A standalone Streamlit application for creating professional labels from data files.
Supports multiple data sources, batch processing, and customizable label designs.
VERSION: 3.5 - STANDALONE LABEL CREATOR
"""

import streamlit as st
import polars as pl
import pandas as pd
import logging
from io import BytesIO, StringIO
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import mm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib import colors

# Configure logging
def setup_logging():
    """Configure logging to write to label_creator.log file with INFO level."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('label_creator.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# Initialize logger
logger = setup_logging()

def apply_custom_css():
    """Apply custom CSS styling to the Streamlit app."""
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global styling */
    .main {
        font-family: 'Inter', sans-serif;
    }
    
    /* Title styling */
    .main-title {
        text-align: center;
        color: #8B4513;
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .subtitle {
        text-align: center;
        color: #666;
        font-size: 1.3rem;
        margin-bottom: 2rem;
    }
    
    /* Step indicators */
    .step-indicator {
        background: linear-gradient(90deg, #8B4513, #A0522D);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        font-size: 1.2rem;
        font-weight: 600;
        margin: 2rem 0 1rem 0;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    /* Upload section styling */
    .upload-section {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 2px dashed #8B4513;
        margin: 1rem 0;
    }
    
    /* Instructions box */
    .instructions {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #8B4513;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

def detect_file_format(file):
    """Detect file format based on file extension."""
    if file is None:
        return 'unknown'
    
    filename = file.name.lower()
    
    if filename.endswith('.csv'):
        return 'csv'
    elif filename.endswith(('.xlsx', '.xls')):
        return 'excel'
    elif filename.endswith('.tsv'):
        return 'tsv'
    elif filename.endswith('.txt'):
        return 'txt'
    else:
        return 'unknown'

class LabelTemplate:
    """Base class for label templates with customizable dimensions and design."""
    
    def __init__(self, width_mm=50, height_mm=30, name="Default Template"):
        self.width_mm = width_mm
        self.height_mm = height_mm
        self.width_points = width_mm * mm
        self.height_points = height_mm * mm
        self.name = name
        self.margin_mm = 2
        self.margin_points = self.margin_mm * mm
        
    def get_dimensions(self):
        """Return template dimensions in points for ReportLab."""
        return (self.width_points, self.height_points)
    
    def get_content_area(self):
        """Return content area dimensions excluding margins."""
        content_width = self.width_points - (2 * self.margin_points)
        content_height = self.height_points - (2 * self.margin_points)
        return (content_width, content_height)

def read_file_with_polars(file, file_format):
    """Read file with Polars for superior performance."""
    try:
        file.seek(0)  # Always reset file pointer
        
        if file_format == 'excel':
            # For Excel files, use pandas then convert to Polars
            try:
                df_pandas = pd.read_excel(file, engine='openpyxl')
                logger.info("Excel file read successfully")
            except Exception:
                try:
                    file.seek(0)
                    df_pandas = pd.read_excel(file)
                    logger.info("Excel file read with default engine")
                except Exception as e:
                    raise ValueError(f"Could not read Excel file: {str(e)}")
            
            return pl.from_pandas(df_pandas)
        
        elif file_format == 'csv':
            try:
                df = pl.read_csv(file, encoding='utf-8-lossy', ignore_errors=True)
                logger.info("CSV file read successfully")
                return df
            except Exception:
                file.seek(0)
                df_pandas = pd.read_csv(file, encoding='utf-8', errors='replace')
                return pl.from_pandas(df_pandas)
        
        elif file_format == 'tsv':
            try:
                df = pl.read_csv(file, separator='\t', encoding='utf-8-lossy', ignore_errors=True)
                logger.info("TSV file read successfully")
                return df
            except Exception:
                file.seek(0)
                df_pandas = pd.read_csv(file, sep='\t', encoding='utf-8', errors='replace')
                return pl.from_pandas(df_pandas)
        
        elif file_format == 'txt':
            # Try different separators for text files
            separators = [',', '\t', ';', '|']
            for sep in separators:
                try:
                    file.seek(0)
                    df = pl.read_csv(file, separator=sep, encoding='utf-8-lossy', ignore_errors=True)
                    if len(df.columns) > 1:
                        logger.info(f"TXT file read with '{sep}' separator")
                        return df
                except Exception:
                    continue
            
            raise ValueError("Could not determine separator for TXT file")
        
        else:
            raise ValueError(f"Unsupported file format: {file_format}")
            
    except Exception as e:
        logger.error(f"Error reading {file_format} file: {str(e)}")
        raise ValueError(f"Could not read {file_format.upper()} file: {str(e)}")

def read_multiple_files(uploaded_files):
    """Read multiple uploaded files and combine them into a single dataset."""
    combined_data = []
    file_info = []
    
    for uploaded_file in uploaded_files:
        try:
            file_format = detect_file_format(uploaded_file)
            df = read_file_with_polars(uploaded_file, file_format)
            
            if not df.is_empty():
                # Add source file column
                df = df.with_columns(pl.lit(uploaded_file.name).alias("Source_File"))
                combined_data.append(df)
                file_info.append({
                    'name': uploaded_file.name,
                    'format': file_format,
                    'rows': df.shape[0],
                    'columns': df.shape[1]
                })
                logger.info(f"Successfully loaded {uploaded_file.name}: {df.shape[0]} rows, {df.shape[1]} columns")
            
        except Exception as e:
            logger.error(f"Error reading file {uploaded_file.name}: {e}")
            st.error(f"❌ Failed to read {uploaded_file.name}: {str(e)}")
    
    # Combine all dataframes
    if combined_data:
        try:
            combined_df = pl.concat(combined_data, how="diagonal")
            logger.info(f"Combined dataset: {combined_df.shape[0]} rows, {combined_df.shape[1]} columns")
            return combined_df, file_info
        except Exception as e:
            logger.error(f"Error combining datasets: {e}")
            return None, file_info
    
    return None, file_info

def generate_batch_labels(data_df, design_config, selected_fields, template):
    """Generate labels for entire dataset."""
    try:
        # Create PDF document
        buffer = BytesIO()
        
        doc = SimpleDocTemplate(buffer, pagesize=letter, 
                              leftMargin=10, rightMargin=10, 
                              topMargin=10, bottomMargin=10)
        
        story = []
        
        # Convert Polars to Pandas for easier iteration
        if hasattr(data_df, 'to_pandas'):
            pandas_df = data_df.to_pandas()
        else:
            pandas_df = data_df
        
        # Generate labels
        for index, row in pandas_df.iterrows():
            label_data = []
            
            # Add selected fields to label
            for field in selected_fields:
                if field in row and pd.notna(row[field]):
                    label_data.append([str(row[field])])
            
            # Create table for label content
            if label_data:
                table = Table(label_data, colWidths=[template.width_points - 20])
                table.setStyle(TableStyle([
                    ('FONTSIZE', (0, 0), (-1, -1), design_config.get('font_size', 12)),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('TEXTCOLOR', (0, 0), (-1, -1), colors.toColor(design_config.get('font_color', '#000000'))),
                    ('BOX', (0, 0), (-1, -1), design_config.get('border_width', 1), colors.black),
                ]))
                story.append(table)
                story.append(Spacer(1, 10))
        
        doc.build(story)
        buffer.seek(0)
        return buffer
        
    except Exception as e:
        logger.error(f"Error generating batch labels: {e}")
        return None

def main():
    """Main application function for Label Creator V3.5."""
    # Page configuration MUST be the first Streamlit command
    st.set_page_config(
        page_title="Label Creator V3.5 - Professional Label Generation",
        page_icon="🏷️",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # Log application start
    logger.info("Label Creator V3.5 session started.")

    # Apply custom styling
    apply_custom_css()

    # Main title and subtitle
    st.markdown('<h1 class="main-title">🏷️ Label Creator V3.5</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Professional Label Generation from Your Data - Standalone & Powerful!</p>', unsafe_allow_html=True)

    # Application overview
    st.markdown("""
    <div class="instructions">
        <h3>🏷️ Professional Label Creation Workflow:</h3>
        <p><strong>Step 1:</strong> Upload Data Files → <strong>Step 2:</strong> Configure Label Design → <strong>Step 3:</strong> Generate & Download Labels</p>
        <br>
        <p><strong>🎯 Perfect for:</strong> Creating labels from V3 Data Merger exports, inventory files, product catalogs, or any structured data.</p>
        <br>
        <p><strong>🚀 V3.5 Features:</strong></p>
        <ul>
            <li><strong>Multi-File Support:</strong> Upload and process multiple data files simultaneously</li>
            <li><strong>Batch Processing:</strong> Generate hundreds of labels in one operation</li>
            <li><strong>Custom Templates:</strong> Professional label designs with your specifications</li>
            <li><strong>Flexible Data Sources:</strong> Works with exports from V3 or any CSV/Excel files</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)

    # Step 1: Data Import
    st.markdown('<div class="step-indicator">📁 Step 1: Import Data Files</div>', unsafe_allow_html=True)

    with st.container():
        # Multi-file upload section
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("📁 Upload Data Files")

        uploaded_files = st.file_uploader(
            "Choose your data files (supports multiple files)",
            type=['csv', 'xlsx', 'xls', 'tsv', 'txt'],
            accept_multiple_files=True,
            help="Upload one or more data files. Perfect for V3 Data Merger exports or any structured data files."
        )

        if uploaded_files:
            st.success(f"✅ {len(uploaded_files)} file(s) uploaded successfully!")

            # Display file information
            for uploaded_file in uploaded_files:
                file_format = detect_file_format(uploaded_file)
                col1, col2, col3 = st.columns([3, 1, 1])
                with col1:
                    st.write(f"📄 {uploaded_file.name}")
                with col2:
                    st.write(f"Format: {file_format.upper()}")
                with col3:
                    st.write(f"Size: {uploaded_file.size} bytes")

                logger.info(f"File uploaded: {uploaded_file.name} (format: {file_format})")

        st.markdown('</div>', unsafe_allow_html=True)

        # Data processing and label creation
        if uploaded_files:
            st.markdown("---")
            st.subheader("🔧 Process Data & Configure Labels")

            # Process uploaded files
            if st.button("📊 Load & Process Data", use_container_width=True, type="primary"):
                with st.spinner("Processing uploaded files..."):
                    try:
                        # Read and combine multiple files
                        combined_df, file_info = read_multiple_files(uploaded_files)

                        if combined_df is not None:
                            # Store in session state
                            st.session_state['combined_data'] = combined_df
                            st.session_state['file_info'] = file_info
                            st.session_state['data_loaded'] = True

                            st.success("✅ Data loaded successfully!")

                            # Display summary
                            st.info(f"📊 **Combined Dataset**: {combined_df.shape[0]} rows, {combined_df.shape[1]} columns")

                            # Show file breakdown
                            with st.expander("📋 File Details", expanded=False):
                                for info in file_info:
                                    st.write(f"• **{info['name']}**: {info['rows']} rows, {info['columns']} columns ({info['format'].upper()})")

                        else:
                            st.error("❌ Failed to process files. Please check your data and try again.")

                    except Exception as e:
                        st.error(f"❌ Error processing files: {str(e)}")
                        logger.error(f"Error processing files: {str(e)}")

    # Step 2: Label Design Studio
    if st.session_state.get('data_loaded', False):
        st.markdown("---")
        st.markdown('<div class="step-indicator">🏷️ Step 2: Label Design Studio</div>', unsafe_allow_html=True)

        combined_df = st.session_state['combined_data']

        with st.expander("🎨 Label Configuration", expanded=True):
            col1, col2 = st.columns([1, 1])

            with col1:
                st.subheader("📐 Label Template")

                # Template selection (placeholder for your custom templates)
                template_type = st.selectbox(
                    "Choose label template:",
                    ["Default Template", "Custom Template 1", "Custom Template 2"],
                    help="Select from pre-designed templates or create custom dimensions"
                )

                # Template dimensions (will be replaced with your specifications)
                col_w, col_h = st.columns(2)
                with col_w:
                    label_width = st.number_input("Width (mm)", min_value=10, max_value=200, value=50)
                with col_h:
                    label_height = st.number_input("Height (mm)", min_value=10, max_value=200, value=30)

                # Create template
                template = LabelTemplate(width_mm=label_width, height_mm=label_height, name=template_type)

            with col2:
                st.subheader("📝 Content Selection")

                # Field selection
                available_columns = [col for col in combined_df.columns if col != "Source_File"]
                selected_fields = st.multiselect(
                    "Select fields to include on labels:",
                    options=available_columns,
                    default=available_columns[:3] if len(available_columns) >= 3 else available_columns,
                    help="Choose which data fields to display on your labels"
                )

                # Design options
                st.subheader("🎨 Design Options")
                font_size = st.slider("Font Size", min_value=8, max_value=24, value=12)
                font_color = st.color_picker("Font Color", "#000000")
                background_color = st.color_picker("Background Color", "#FFFFFF")
                border_width = st.slider("Border Width", min_value=0, max_value=5, value=1)

        # Preview section
        with st.expander("👁️ Label Preview", expanded=True):
            if selected_fields:
                st.subheader("🔍 Sample Label Preview")

                # Show preview of first row
                if not combined_df.is_empty():
                    sample_row = combined_df.head(1).to_pandas().iloc[0]

                    # Create preview table
                    preview_data = []
                    for field in selected_fields:
                        if field in sample_row:
                            preview_data.append([f"{field}: {sample_row[field]}"])

                    if preview_data:
                        st.table(preview_data)
                        st.info(f"📏 Label Size: {label_width}mm × {label_height}mm")
                        st.info(f"🎨 Style: {font_size}pt font, {font_color} text on {background_color} background")
            else:
                st.warning("⚠️ Please select at least one field to include on labels")

        # Generate labels section
        if selected_fields:
            st.markdown("---")
            st.subheader("🏷️ Generate Labels")

            col1, col2, col3 = st.columns([1, 1, 1])

            with col1:
                total_labels = combined_df.shape[0]
                st.metric("Total Labels", total_labels)

            with col2:
                if st.button("🔍 Generate Preview (First 5)", use_container_width=True):
                    with st.spinner("Generating preview labels..."):
                        try:
                            # Generate preview with first 5 rows
                            preview_df = combined_df.head(5)
                            design_config = {
                                'font_size': font_size,
                                'font_color': font_color,
                                'background_color': background_color,
                                'border_width': border_width
                            }

                            pdf_buffer = generate_batch_labels(preview_df, design_config, selected_fields, template)

                            if pdf_buffer:
                                st.success("✅ Preview generated successfully!")
                                st.download_button(
                                    label="📥 Download Preview PDF",
                                    data=pdf_buffer.getvalue(),
                                    file_name="label_preview.pdf",
                                    mime="application/pdf"
                                )
                            else:
                                st.error("❌ Failed to generate preview")

                        except Exception as e:
                            st.error(f"❌ Preview generation failed: {str(e)}")
                            logger.error(f"Preview generation failed: {str(e)}")

            with col3:
                if st.button("🏷️ Generate All Labels", use_container_width=True, type="primary"):
                    with st.spinner(f"Generating {total_labels} labels..."):
                        try:
                            design_config = {
                                'font_size': font_size,
                                'font_color': font_color,
                                'background_color': background_color,
                                'border_width': border_width
                            }

                            pdf_buffer = generate_batch_labels(combined_df, design_config, selected_fields, template)

                            if pdf_buffer:
                                st.success(f"✅ {total_labels} labels generated successfully!")
                                st.download_button(
                                    label="📥 Download All Labels PDF",
                                    data=pdf_buffer.getvalue(),
                                    file_name="all_labels.pdf",
                                    mime="application/pdf"
                                )

                                # Store in session state for further processing
                                st.session_state['labels_generated'] = True
                                st.session_state['label_pdf'] = pdf_buffer

                            else:
                                st.error("❌ Failed to generate labels")

                        except Exception as e:
                            st.error(f"❌ Label generation failed: {str(e)}")
                            logger.error(f"Label generation failed: {str(e)}")

    # Step 3: Additional Options & Information
    if st.session_state.get('labels_generated', False):
        st.markdown("---")
        st.markdown('<div class="step-indicator">📋 Step 3: Additional Options</div>', unsafe_allow_html=True)

        st.success("🎉 **Labels Generated Successfully!**")
        st.info("Your labels are ready for download. You can generate different label designs by going back to Step 2 and adjusting the configuration.")

        # Additional export options could go here
        with st.expander("📊 Session Summary", expanded=False):
            if 'file_info' in st.session_state:
                st.write("**Files Processed:**")
                for info in st.session_state['file_info']:
                    st.write(f"• {info['name']} - {info['rows']} rows")

            if 'combined_data' in st.session_state:
                total_records = st.session_state['combined_data'].shape[0]
                st.write(f"**Total Records Processed:** {total_records}")
                st.write(f"**Labels Generated:** {total_records}")

if __name__ == "__main__":
    main()
