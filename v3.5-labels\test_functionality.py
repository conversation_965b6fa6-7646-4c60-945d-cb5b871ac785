"""
Test script to verify V3 functionality
"""
import polars as pl
import pandas as pd
from io import StringIO

def test_polars_functionality():
    """Test basic Polars functionality"""
    print("Testing Polars functionality...")
    
    # Create test data
    pos_data = {
        'UPC': ['123', '456', '789'],
        'Product': ['A', 'B', 'C'],
        'Price': [10.0, 20.0, 30.0]
    }
    
    supplier_data = {
        'UPC': ['123', '456', '999'],
        'Supplier': ['X', 'Y', 'Z'],
        'Cost': [5.0, 10.0, 15.0]
    }
    
    # Create Polars DataFrames
    pos_df = pl.DataFrame(pos_data)
    supplier_df = pl.DataFrame(supplier_data)
    
    print(f"POS DataFrame: {pos_df.shape}")
    print(f"Supplier DataFrame: {supplier_df.shape}")
    
    # Test different merge types
    print("\nTesting Inner Join:")
    inner_result = pos_df.join(supplier_df, on="UPC", how="inner")
    print(f"Inner join result: {inner_result.shape[0]} rows")
    print(inner_result)
    
    print("\nTesting Left Join:")
    left_result = pos_df.join(supplier_df, on="UPC", how="left")
    print(f"Left join result: {left_result.shape[0]} rows")
    print(left_result)
    
    print("\nTesting Outer Join:")
    outer_result = pos_df.join(supplier_df, on="UPC", how="outer")
    print(f"Outer join result: {outer_result.shape[0]} rows")
    print(outer_result)
    
    print("\n✅ All Polars tests passed!")

def test_file_reading():
    """Test file reading functionality"""
    print("\nTesting file reading...")
    
    # Create test CSV content
    csv_content = """UPC,Product,Price
123,Widget A,10.99
456,Widget B,15.50
789,Widget C,7.25"""
    
    # Test CSV reading
    try:
        df = pl.read_csv(StringIO(csv_content))
        print(f"✅ CSV reading successful: {df.shape}")
        print(df)
    except Exception as e:
        print(f"❌ CSV reading failed: {e}")
    
    print("\n✅ File reading tests completed!")

if __name__ == "__main__":
    test_polars_functionality()
    test_file_reading()
    print("\n🎉 All tests completed!")
