"""
Test script to verify UPC hyphen fix
"""
import pandas as pd
import polars as pl

def test_upc_hyphen_fix():
    """Test that UPC values with hyphens are handled correctly"""
    print("Testing UPC hyphen fix...")
    
    # Create test data with problematic UPC values
    test_data = {
        'UPC': ['111-690', '123456789012', 'ABC-123', '999-888-777'],
        'Product': ['Product A', 'Product B', 'Product C', 'Product D'],
        'Price': [10.99, 15.50, 7.25, 12.00]
    }
    
    # Create pandas DataFrame
    df_pandas = pd.DataFrame(test_data)
    print(f"Pandas DataFrame created with UPC types: {df_pandas['UPC'].dtype}")
    print(f"UPC values: {df_pandas['UPC'].tolist()}")
    
    try:
        # Test conversion without schema override (should fail)
        print("\nTesting conversion WITHOUT schema override...")
        polars_df_no_override = pl.from_pandas(df_pandas)
        print(f"❌ Unexpected success without override: {polars_df_no_override['UPC'].dtype}")
    except Exception as e:
        print(f"✅ Expected failure without override: {str(e)}")
    
    try:
        # Test conversion WITH schema override (should work)
        print("\nTesting conversion WITH schema override...")
        schema_overrides = {'UPC': pl.Utf8}
        polars_df = pl.from_pandas(df_pandas, schema_overrides=schema_overrides)
        print(f"✅ Success with schema override!")
        print(f"UPC dtype: {polars_df['UPC'].dtype}")
        print(f"UPC values preserved: {polars_df['UPC'].to_list()}")
        
        # Verify all UPC values are preserved correctly
        original_upcs = set(df_pandas['UPC'].tolist())
        converted_upcs = set(polars_df['UPC'].to_list())
        
        if original_upcs == converted_upcs:
            print("✅ All UPC values preserved correctly")
        else:
            print(f"❌ UPC values changed: {original_upcs} -> {converted_upcs}")
            
        return True
        
    except Exception as e:
        print(f"❌ Schema override failed: {str(e)}")
        return False

def test_app_import():
    """Test that the app imports successfully with fixes"""
    print("\nTesting app import...")
    try:
        import app
        print("✅ App imported successfully")
        
        # Test the validation function
        if hasattr(app, 'validate_and_clean_upc_column'):
            print("✅ UPC validation function available")
        else:
            print("❌ UPC validation function missing")
            
        return True
    except Exception as e:
        print(f"❌ App import failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 UPC Hyphen Fix Test")
    print("=" * 50)
    
    success1 = test_upc_hyphen_fix()
    success2 = test_app_import()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All tests passed! UPC hyphen fix is working.")
    else:
        print("❌ Some tests failed. Check the output above.")
