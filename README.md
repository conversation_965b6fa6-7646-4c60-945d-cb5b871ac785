# POS & Supplier Data Merger

A professional Streamlit web application for merging POS (Point of Sale) and Supplier data files based on UPC (Universal Product Code) columns. Available in two versions to meet different needs.

## 🚀 Live Deployments

- **V1 (Stable)**: [Deploy on Streamlit Cloud](https://share.streamlit.io/) - CSV Only
- **V2 (Multi-Format)**: [Deploy on Streamlit Cloud](https://share.streamlit.io/) - Multiple Formats
- **V3 (Interactive)**: [Deploy on Streamlit Cloud](https://share.streamlit.io/) - Advanced Workflow

## 📁 Project Structure

```
Matcher/
├── v1-stable/              # Version 1 - Stable CSV-only version
│   ├── app.py             # Main application
│   ├── requirements.txt   # Dependencies
│   ├── README.md         # V1 documentation
│   └── sample_*.csv      # Sample CSV files
├── v2-multiformat/        # Version 2 - Multi-format version
│   ├── app.py            # Main application
│   ├── requirements.txt  # Dependencies (includes Excel support)
│   ├── README.md        # V2 documentation
│   └── sample_*.*       # Sample files (CSV, TSV, TXT)
├── v3-interactive/        # Version 3 - Interactive workflow
│   ├── app.py            # Main application (Polars-powered)
│   ├── requirements.txt  # Dependencies (includes Polars)
│   ├── README.md        # V3 documentation
│   └── sample_*.*       # Sample files (CSV, TSV, TXT)
└── README.md            # This file
```

## 🎯 Version Comparison

| Feature | V1 (Stable) | V2 (Multi-Format) | V3 (Interactive) |
|---------|-------------|-------------------|------------------|
| **File Formats** | CSV only | CSV, Excel, TSV, TXT | CSV, Excel, TSV, TXT |
| **Data Engine** | Pandas | Pandas | **Polars** (High Performance) |
| **Merge Types** | Inner only | Inner only | Inner, Left, Outer |
| **Transformations** | None | None | **Interactive Studio** |
| **Export Formats** | CSV | CSV | Excel, JSON, Parquet |
| **Excel Export** | Basic | Basic | **3 Sheets (Intelligent)** |
| **Use Case** | Simple merging | Multi-format merging | **Advanced Workflow** |
| **Performance** | Good | Good | **Superior** |

## 🔧 Quick Start

### V1 - Stable (CSV Only)
```bash
cd v1-stable
pip install -r requirements.txt
streamlit run app.py
```

### V2 - Multi-Format
```bash
cd v2-multiformat
pip install -r requirements.txt
streamlit run app.py
```

### V3 - Interactive Workflow
```bash
cd v3-interactive
pip install -r requirements.txt
streamlit run app.py
```

### V3.5 - Label Creator
```bash
cd v3.5-labels
pip install -r requirements.txt
streamlit run app.py
```

## 📊 Features

### Core Features (Both Versions)
- 🎨 **Modern UI**: Professional design with custom CSS
- 📊 **Smart Merging**: Handles UPC data type mismatches
- 📝 **Comprehensive Logging**: Detailed operation tracking
- 🛡️ **Error Handling**: Robust error management
- 🔄 **Real-time Feedback**: Progress indicators
- 📥 **Easy Download**: One-click CSV export

### V2 Additional Features
- 📄 **Multiple Formats**: CSV, Excel (.xlsx/.xls), TSV, TXT
- 🔍 **Auto-Detection**: Automatic file format recognition
- 🔄 **Mixed Formats**: Different formats for POS and Supplier files
- 📋 **Smart Separators**: Auto-detects delimiters in text files

### V3 Revolutionary Features
- ⚡ **Polars Engine**: Superior performance for large datasets
- 🎛️ **Transformation Studio**: Interactive data cleaning and filtering
- 🔧 **Smart Merge Control**: Inner, Left, and Outer joins
- 📊 **Intelligent Excel Export**: Separate sheets for matched/unmatched data
- 📤 **Multi-Format Export**: Excel, JSON, Parquet support
- 🔄 **Live Preview**: Real-time transformation feedback

### V3.5 Standalone Features
- 🏷️ **Professional Label Creator**: Standalone label generation application
- 📁 **Multi-File Processing**: Upload and combine multiple data sources
- 🎨 **Custom Templates**: Configurable label dimensions and designs
- 📤 **PDF Export**: High-quality, printable labels
- 🔄 **Batch Processing**: Generate hundreds of labels efficiently
- 🎯 **Perfect Integration**: Works seamlessly with V3 exports

## 🚀 Deployment Guide

### Streamlit Cloud Deployment

1. **Fork this repository** to your GitHub account

2. **For V1 Deployment:**
   - Go to [Streamlit Cloud](https://share.streamlit.io/)
   - Connect your GitHub repository
   - Set app path: `v1-stable/app.py`
   - Deploy

3. **For V2 Deployment:**
   - Create a new app on Streamlit Cloud
   - Set app path: `v2-multiformat/app.py`
   - Deploy

4. **For V3 Deployment:**
   - Create a new app on Streamlit Cloud
   - Set app path: `v3-interactive/app.py`
   - Deploy

5. **For V3.5 Deployment:**
   - Create a new app on Streamlit Cloud
   - Set app path: `v3.5-labels/app.py`
   - Deploy

### Local Development

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/matcher.git
   cd matcher
   ```

2. **Choose your version and run:**
   ```bash
   # For V1
   cd v1-stable && streamlit run app.py

   # For V2
   cd v2-multiformat && streamlit run app.py

   # For V3
   cd v3-interactive && streamlit run app.py

   # For V3.5
   cd v3.5-labels && streamlit run app.py
   ```

## 📋 Requirements

### V1 Requirements
- Python 3.7+
- Streamlit 1.28.0+
- Pandas 2.0.0+

### V2 Requirements
- Python 3.7+
- Streamlit 1.28.0+
- Pandas 2.0.0+
- openpyxl 3.1.0+ (Excel support)
- xlrd 2.0.1+ (Legacy Excel support)

### V3 Requirements
- Python 3.7+
- Streamlit 1.28.0+
- **Polars 0.20.0+** (High-performance engine)
- Pandas 2.0.0+ (Excel compatibility)
- openpyxl 3.1.0+ (Excel export)
- xlrd 2.0.1+ (Legacy Excel support)

### V3.5 Requirements
- Python 3.7+
- Streamlit 1.28.0+
- **Polars 0.20.0+** (High-performance data processing)
- **ReportLab 4.0.0+** (Professional PDF generation)
- Pandas 2.0.0+ (Excel compatibility)
- openpyxl 3.1.0+ (Excel support)

## 🎯 Use Cases

### V1 - Perfect For:
- Simple CSV data merging
- Lightweight deployments
- Minimal dependencies
- Quick prototyping

### V2 - Perfect For:
- Complex data integration
- Multiple data sources
- Enterprise environments
- Advanced file format support

### V3 - Perfect For:
- **Advanced data workflows**
- **Large dataset processing**
- **Interactive data cleaning**
- **Professional data analysis**
- **Multi-format exports**
- **Audit-ready reporting**

### V3.5 - Perfect For:
- **Professional label creation**
- **Inventory management**
- **Product labeling**
- **Asset tracking**
- **Batch label generation**
- **Print-ready outputs**

## 📖 Documentation

- **V1 Documentation**: See `v1-stable/README.md`
- **V2 Documentation**: See `v2-multiformat/README.md`
- **V3 Documentation**: See `v3-interactive/README.md`
- **V3.5 Documentation**: See `v3.5-labels/README.md`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🆘 Support

- **Issues**: Create an issue on GitHub
- **Documentation**: Check version-specific README files
- **Logs**: Check `app.log` files for debugging

---

**Choose the version that best fits your needs:**
- **Need simplicity?** → Use V1
- **Need flexibility?** → Use V2
- **Need advanced workflows?** → Use V3
- **Need professional labels?** → Use V3.5
