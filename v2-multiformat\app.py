"""
POS & Supplier Data Merger (V2 - MULTI-FORMAT)
A Streamlit application for merging POS and Supplier data files based on UPC column.
Supports multiple file formats: CSV, Excel (XLS/XLSX), TSV, and TXT files.
Features comprehensive logging and modern UI design.
VERSION: 2.0 - MULTI-FORMAT SUPPORT
"""

import streamlit as st
import pandas as pd
import logging

# Configure logging
def setup_logging():
    """Configure logging to write to app.log file with INFO level."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app_v2.log'),
            logging.StreamHandler()  # Also log to console for debugging
        ]
    )
    return logging.getLogger(__name__)

# Initialize logger
logger = setup_logging()

def apply_custom_css():
    """Apply custom CSS for modern, professional styling."""
    st.markdown("""
    <style>
    /* Main app styling */
    .main {
        padding-top: 2rem;
    }
    
    /* Title styling */
    .main-title {
        text-align: center;
        color: #0047AB;
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .subtitle {
        text-align: center;
        color: #666;
        font-size: 1.2rem;
        margin-bottom: 2rem;
    }
    
    /* Instructions styling */
    .instructions {
        background: linear-gradient(135deg, #f0f2f6 0%, #e8eaf6 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #0047AB;
        margin-bottom: 2rem;
    }
    
    /* File upload sections */
    .upload-section {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 2px solid #f0f2f6;
        transition: border-color 0.3s ease;
    }
    
    .upload-section:hover {
        border-color: #0047AB;
    }
    
    /* Success message styling */
    .success-box {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin: 1rem 0;
        font-weight: 600;
    }
    
    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #FFA500 0%, #ff8c00 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255,165,0,0.3);
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255,165,0,0.4);
    }
    
    /* Download button styling */
    .stDownloadButton > button {
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(76,175,80,0.3);
    }
    
    .stDownloadButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(76,175,80,0.4);
    }
    
    /* Data preview styling */
    .dataframe {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    /* Format info styling */
    .format-info {
        background: #e3f2fd;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #2196F3;
        margin: 1rem 0;
        font-size: 0.9rem;
    }
    </style>
    """, unsafe_allow_html=True)

def convert_df_to_csv(df):
    """Convert DataFrame to CSV format for download."""
    return df.to_csv(index=False).encode('utf-8')

def detect_file_format(file):
    """
    Detect file format based on file extension.
    
    Args:
        file: Uploaded file object
    
    Returns:
        str: File format ('csv', 'excel', 'tsv', 'txt', 'unknown')
    """
    if file is None:
        return 'unknown'
    
    filename = file.name.lower()
    
    if filename.endswith('.csv'):
        return 'csv'
    elif filename.endswith(('.xlsx', '.xls')):
        return 'excel'
    elif filename.endswith('.tsv'):
        return 'tsv'
    elif filename.endswith('.txt'):
        return 'txt'
    else:
        return 'unknown'

def read_file_with_encoding(file, file_format):
    """
    Read file with multiple encoding attempts based on format.
    
    Args:
        file: Uploaded file object
        file_format: Detected file format
    
    Returns:
        pandas.DataFrame: Loaded data
    """
    encodings_to_try = ['utf-8', 'latin-1', 'iso-8859-1', 'cp1252']
    
    if file_format == 'excel':
        # Excel files don't need encoding specification
        try:
            return pd.read_excel(file)
        except Exception as e:
            raise ValueError(f"Could not read Excel file: {str(e)}")
    
    # For text-based formats (CSV, TSV, TXT)
    separators = {
        'csv': ',',
        'tsv': '\t',
        'txt': None  # Will try to auto-detect
    }
    
    separator = separators.get(file_format, ',')
    
    for encoding in encodings_to_try:
        try:
            file.seek(0)  # Reset file pointer
            
            if file_format == 'txt':
                # For TXT files, try different separators
                for sep in [',', '\t', ';', '|']:
                    try:
                        file.seek(0)
                        df = pd.read_csv(file, encoding=encoding, encoding_errors='replace', sep=sep)
                        if len(df.columns) > 1:  # If we got multiple columns, separator worked
                            logger.info(f"TXT file successfully read with {encoding} encoding and '{sep}' separator")
                            return df
                    except:
                        continue
            else:
                df = pd.read_csv(file, encoding=encoding, encoding_errors='replace', sep=separator)
                logger.info(f"{file_format.upper()} file successfully read with {encoding} encoding")
                return df
                
        except (UnicodeDecodeError, UnicodeError):
            continue
        except Exception as e:
            logger.warning(f"Error reading {file_format} file with {encoding}: {str(e)}")
            continue
    
    raise ValueError(f"Could not read {file_format.upper()} file with any supported encoding")

def process_and_merge_data(pos_file, supplier_file):
    """
    Process and merge POS and Supplier data files.
    Supports multiple file formats: CSV, Excel, TSV, TXT.
    
    Args:
        pos_file: Uploaded POS data file
        supplier_file: Uploaded Supplier data file
    
    Returns:
        tuple: (merged_dataframe, success_flag, error_message)
    """
    try:
        logger.info("Starting data merge process...")
        
        # Detect file formats
        pos_format = detect_file_format(pos_file)
        supplier_format = detect_file_format(supplier_file)
        
        logger.info(f"Detected POS file format: {pos_format}")
        logger.info(f"Detected Supplier file format: {supplier_format}")
        
        # Check for unsupported formats
        supported_formats = ['csv', 'excel', 'tsv', 'txt']
        if pos_format not in supported_formats:
            raise ValueError(f"Unsupported POS file format: {pos_format}. Supported formats: {', '.join(supported_formats)}")
        if supplier_format not in supported_formats:
            raise ValueError(f"Unsupported Supplier file format: {supplier_format}. Supported formats: {', '.join(supported_formats)}")
        
        # Read files based on their formats
        pos_df = read_file_with_encoding(pos_file, pos_format)
        supplier_df = read_file_with_encoding(supplier_file, supplier_format)
        
        logger.info(f"POS data loaded: {pos_df.shape[0]} rows, {pos_df.shape[1]} columns")
        logger.info(f"Supplier data loaded: {supplier_df.shape[0]} rows, {supplier_df.shape[1]} columns")
        
        # Check if UPC column exists in both files
        if 'UPC' not in pos_df.columns:
            raise ValueError("UPC column not found in POS file")
        if 'UPC' not in supplier_df.columns:
            raise ValueError("UPC column not found in Supplier file")
        
        # Critical step: Convert UPC columns to string type to handle data type mismatches
        pos_df['UPC'] = pos_df['UPC'].astype(str)
        supplier_df['UPC'] = supplier_df['UPC'].astype(str)
        
        logger.info("UPC columns converted to string type for accurate matching")
        
        # Perform inner merge on UPC column
        merged_df = pd.merge(pos_df, supplier_df, on='UPC', how='inner')
        
        logger.info(f"Data merge successful. Merged data has {merged_df.shape[0]} rows and {merged_df.shape[1]} columns.")
        
        return merged_df, True, None
        
    except Exception as e:
        error_msg = f"Error during data merge process: {str(e)}"
        logger.error(error_msg)
        return None, False, error_msg

def main():
    """Main application function."""
    # Page configuration MUST be the first Streamlit command
    st.set_page_config(
        page_title="POS & Supplier Data Merger V2",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="collapsed"
    )
    
    # Log application start
    logger.info("Application V2 session started.")
    
    # Apply custom styling
    apply_custom_css()
    
    # Main title and subtitle
    st.markdown('<h1 class="main-title">📊 POS & Supplier Data Merger V2</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Merge your POS and Supplier data files seamlessly - Now supports multiple formats!</p>', unsafe_allow_html=True)
    
    # Format support info
    st.markdown("""
    <div class="format-info">
        <h4>🎯 Supported File Formats:</h4>
        <p><strong>📄 CSV</strong> (.csv) • <strong>📊 Excel</strong> (.xlsx, .xls) • <strong>📋 TSV</strong> (.tsv) • <strong>📝 Text</strong> (.txt)</p>
        <p>The application automatically detects the format and handles encoding issues.</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Instructions
    st.markdown("""
    <div class="instructions">
        <h3>📋 How to Use:</h3>
        <p><strong>Step 1:</strong> Upload both your POS and Supplier data files (must contain UPC column)</p>
        <p><strong>Step 2:</strong> Click 'Merge Files' to combine the data</p>
        <p><strong>Step 3:</strong> Download your merged file</p>
        <br>
        <p><strong>💡 Tips:</strong></p>
        <ul>
            <li>Files can be in different formats (e.g., POS.csv + Supplier.xlsx)</li>
            <li>For TXT files, the app will auto-detect separators (comma, tab, semicolon, pipe)</li>
            <li>If you encounter issues, try saving files as UTF-8 format</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)

    # Create two columns for file uploads
    col1, col2 = st.columns(2)

    with col1:
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("📁 Upload POS File")
        pos_file = st.file_uploader(
            "Choose POS data file",
            type=['csv', 'xlsx', 'xls', 'tsv', 'txt'],
            key="pos_uploader",
            help="Upload your POS data file (CSV, Excel, TSV, or TXT) containing UPC column"
        )
        if pos_file:
            file_format = detect_file_format(pos_file)
            st.success(f"✅ Uploaded: {pos_file.name}")
            st.info(f"📄 Detected format: {file_format.upper()}")
            logger.info(f"POS file uploaded: {pos_file.name} (format: {file_format})")
        st.markdown('</div>', unsafe_allow_html=True)

    with col2:
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        st.subheader("📁 Upload Supplier File")
        supplier_file = st.file_uploader(
            "Choose Supplier data file",
            type=['csv', 'xlsx', 'xls', 'tsv', 'txt'],
            key="supplier_uploader",
            help="Upload your Supplier data file (CSV, Excel, TSV, or TXT) containing UPC column"
        )
        if supplier_file:
            file_format = detect_file_format(supplier_file)
            st.success(f"✅ Uploaded: {supplier_file.name}")
            st.info(f"📄 Detected format: {file_format.upper()}")
            logger.info(f"Supplier file uploaded: {supplier_file.name} (format: {file_format})")
        st.markdown('</div>', unsafe_allow_html=True)

    # Merge button (only enabled when both files are uploaded)
    st.markdown("<br>", unsafe_allow_html=True)

    if pos_file and supplier_file:
        col_center = st.columns([1, 2, 1])[1]
        with col_center:
            if st.button("🔄 Merge Files", use_container_width=True):
                with st.spinner("Merging data... Please wait"):
                    merged_df, success, error_msg = process_and_merge_data(pos_file, supplier_file)

                if success:
                    st.session_state['merged_df'] = merged_df
                    st.session_state['merge_success'] = True
                    st.markdown(
                        '<div class="success-box">✅ Success! Your files have been merged. Click below to download.</div>',
                        unsafe_allow_html=True
                    )
                else:
                    st.error(f"❌ Merge failed: {error_msg}")
    else:
        col_center = st.columns([1, 2, 1])[1]
        with col_center:
            st.button("🔄 Merge Files", disabled=True, use_container_width=True)
            if not pos_file or not supplier_file:
                st.info("Please upload both files to enable merging")

    # Display merged data and download option
    if st.session_state.get('merge_success', False) and 'merged_df' in st.session_state:
        merged_df = st.session_state['merged_df']

        st.markdown("<br>", unsafe_allow_html=True)
        st.subheader("📊 Merged Data Preview")
        st.dataframe(merged_df.head(), use_container_width=True)

        st.info(f"📈 Total merged records: {len(merged_df)} rows, {len(merged_df.columns)} columns")

        # Download button
        csv_data = convert_df_to_csv(merged_df)
        col_download = st.columns([1, 2, 1])[1]
        with col_download:
            if st.download_button(
                label="📥 Download Merged CSV",
                data=csv_data,
                file_name="Merged_POS_and_Supplier_Data_V2.csv",
                mime="text/csv",
                use_container_width=True
            ):
                logger.info("User downloaded the merged CSV file from V2.")

if __name__ == "__main__":
    main()
