"""
Simple test for UPC matching logic
"""
import polars as pl

def test_upc_normalization():
    """Test UPC normalization function"""
    print("Testing UPC normalization...")
    
    from app import normalize_upc_format
    
    test_cases = [
        ('0123456789012', '123456789012'),  # Remove leading zero
        ('123456789', '123456789'),        # No change needed
        ('000123', '123'),                 # Remove leading zeros
        ('111-690', '111-690'),           # Keep hyphens
        ('123.0', '123'),                 # Remove .0
        ('nan', ''),                      # Clean nan
    ]
    
    for input_upc, expected in test_cases:
        result = normalize_upc_format(input_upc)
        status = "✅" if result == expected else "❌"
        print(f"{status} '{input_upc}' -> '{result}' (expected: '{expected}')")
    
    print()

def test_upc_matching_scenario():
    """Test a realistic UPC matching scenario"""
    print("Testing realistic UPC matching scenario...")
    
    # Create data that should match after normalization
    # Scenario: POS has UPCs without leading zeros, Supplier has same UPCs with leading zeros
    pos_data = {
        'UPC': ['123456789012', '234567890123', '345678901234'],  # Standard 12-digit UPCs
        'Product': ['Product A', 'Product B', 'Product C']
    }

    supplier_data = {
        'UPC': ['0123456789012', '0234567890123', '9999999999999'],  # Same UPCs with leading zeros
        'Supplier': ['Supplier X', 'Supplier Y', 'Supplier Z']
    }
    
    pos_df = pl.DataFrame(pos_data)
    supplier_df = pl.DataFrame(supplier_data)
    
    print(f"POS UPCs: {pos_df['UPC'].to_list()}")
    print(f"Supplier UPCs: {supplier_df['UPC'].to_list()}")
    
    # Apply normalization
    from app import validate_and_clean_upc_column
    
    pos_df_clean = validate_and_clean_upc_column(pos_df, "pos")
    supplier_df_clean = validate_and_clean_upc_column(supplier_df, "supplier")
    
    print(f"POS normalized: {pos_df_clean['UPC_normalized'].to_list()}")
    print(f"Supplier normalized: {supplier_df_clean['UPC_normalized'].to_list()}")
    
    # Check for matches
    pos_norm_set = set(pos_df_clean['UPC_normalized'].to_list())
    supplier_norm_set = set(supplier_df_clean['UPC_normalized'].to_list())
    matches = pos_norm_set.intersection(supplier_norm_set)
    
    print(f"Matches found: {len(matches)}")
    if matches:
        print(f"Matching normalized UPCs: {list(matches)}")
        return True
    else:
        print("No matches found")
        return False

def test_excel_conversion():
    """Test Excel conversion with problematic UPCs"""
    print("\nTesting Excel conversion with problematic UPCs...")
    
    import pandas as pd
    
    # Create test data with hyphenated UPCs
    test_data = {
        'UPC': ['111-690', '222-780', '123456789012'],
        'Product': ['Item A', 'Item B', 'Item C']
    }
    
    df_pandas = pd.DataFrame(test_data)
    print(f"Pandas DataFrame: {df_pandas['UPC'].tolist()}")
    
    # Test conversion to Polars with schema override
    try:
        schema_overrides = {'UPC': pl.Utf8}
        polars_df = pl.from_pandas(df_pandas, schema_overrides=schema_overrides)
        print(f"✅ Polars conversion successful: {polars_df['UPC'].to_list()}")
        print(f"UPC column type: {polars_df['UPC'].dtype}")
        return True
    except Exception as e:
        print(f"❌ Polars conversion failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Simple UPC Tests")
    print("=" * 40)
    
    test_upc_normalization()
    result1 = test_upc_matching_scenario()
    result2 = test_excel_conversion()
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"UPC Matching: {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"Excel Conversion: {'✅ PASS' if result2 else '❌ FAIL'}")
    
    if result1 and result2:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️ Some tests failed - check output above")
