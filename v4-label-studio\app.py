"""
Data-Driven Label Studio V4 - Professional Interactive Label Design Application
A sophisticated Streamlit application that emulates professional label design software like BarTender.
Features a three-step workflow: Data Connection → Visual Label Design → Batch PDF Generation.
VERSION: 4.0 - INTERACTIVE LABEL DESIGN STUDIO
"""

import streamlit as st
import polars as pl
import pandas as pd
import logging
import json
import base64
from io import BytesIO
from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode.writer import ImageWriter
from fpdf import FPDF
from streamlit_drawable_canvas import st_canvas
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import io

# Configure logging
def setup_logging():
    """Configure logging to write to label_studio.log file with INFO level."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('label_studio.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# Initialize logger
logger = setup_logging()

def apply_custom_css():
    """Apply custom CSS styling for professional UI."""
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global styling */
    .main {
        font-family: 'Inter', sans-serif;
    }
    
    /* Title styling */
    .main-title {
        text-align: center;
        color: #2E86AB;
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
    
    .subtitle {
        text-align: center;
        color: #666;
        font-size: 1.3rem;
        margin-bottom: 2rem;
    }
    
    /* Step indicators */
    .step-indicator {
        background: linear-gradient(90deg, #2E86AB, #A23B72);
        color: white;
        padding: 1rem 2rem;
        border-radius: 10px;
        font-size: 1.2rem;
        font-weight: 600;
        margin: 2rem 0 1rem 0;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    }
    
    /* Canvas container */
    .canvas-container {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        border: 2px solid #2E86AB;
        margin: 1rem 0;
    }
    
    /* Toolbox styling */
    .toolbox {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #2E86AB;
        margin: 1rem 0;
    }
    
    /* Properties panel */
    .properties-panel {
        background-color: #ffffff;
        padding: 1.5rem;
        border-radius: 10px;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* Upload section styling */
    .upload-section {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 2px dashed #2E86AB;
        margin: 1rem 0;
    }
    </style>
    """, unsafe_allow_html=True)

def detect_file_format(file):
    """Detect file format based on file extension."""
    if file is None:
        return 'unknown'
    
    filename = file.name.lower()
    
    if filename.endswith('.csv'):
        return 'csv'
    elif filename.endswith(('.xlsx', '.xls')):
        return 'excel'
    elif filename.endswith('.tsv'):
        return 'tsv'
    elif filename.endswith('.txt'):
        return 'txt'
    else:
        return 'unknown'

def read_file_with_polars(file, file_format):
    """Read file with Polars for superior performance."""
    try:
        file.seek(0)  # Always reset file pointer

        if file_format == 'excel':
            # For Excel files, use pandas then convert to Polars
            try:
                df_pandas = pd.read_excel(file, engine='openpyxl')
                logger.info("Excel file read successfully")
            except Exception:
                try:
                    file.seek(0)
                    df_pandas = pd.read_excel(file)
                    logger.info("Excel file read with default engine")
                except Exception as e:
                    raise ValueError(f"Could not read Excel file: {str(e)}")

            return pl.from_pandas(df_pandas)

        elif file_format == 'csv':
            try:
                df = pl.read_csv(file, encoding='utf-8-lossy', ignore_errors=True)
                logger.info("CSV file read successfully")
                return df
            except Exception:
                file.seek(0)
                df_pandas = pd.read_csv(file, encoding='utf-8', errors='replace')
                return pl.from_pandas(df_pandas)

        elif file_format == 'tsv':
            try:
                df = pl.read_csv(file, separator='\t', encoding='utf-8-lossy', ignore_errors=True)
                logger.info("TSV file read successfully")
                return df
            except Exception:
                file.seek(0)
                df_pandas = pd.read_csv(file, sep='\t', encoding='utf-8', errors='replace')
                return pl.from_pandas(df_pandas)

        elif file_format == 'txt':
            # Try different separators for text files
            separators = [',', '\t', ';', '|']
            for sep in separators:
                try:
                    file.seek(0)
                    df = pl.read_csv(file, separator=sep, encoding='utf-8-lossy', ignore_errors=True)
                    if len(df.columns) > 1:
                        logger.info(f"TXT file read with '{sep}' separator")
                        return df
                except Exception:
                    continue

            raise ValueError("Could not determine separator for TXT file")

        else:
            raise ValueError(f"Unsupported file format: {file_format}")

    except Exception as e:
        logger.error(f"Error reading {file_format} file: {str(e)}")
        raise ValueError(f"Could not read {file_format.upper()} file: {str(e)}")

def load_data_file(uploaded_file):
    """Load a single data file and return as Polars DataFrame."""
    try:
        file_format = detect_file_format(uploaded_file)
        df = read_file_with_polars(uploaded_file, file_format)

        logger.info(f"Data file loaded successfully: {df.shape[0]} rows, {df.shape[1]} columns")
        return df

    except Exception as e:
        logger.error(f"Error loading data file: {str(e)}")
        raise ValueError(f"Could not load data file: {str(e)}")

def generate_barcode_image(upc_code, width=200, height=100):
    """Generate barcode image from UPC code."""
    try:
        # Validate and clean UPC code
        if not upc_code or str(upc_code).strip() == '':
            logger.warning("Empty UPC code provided, using default")
            upc_code = "123456789012"

        # Clean the UPC code - remove any non-alphanumeric characters
        clean_upc = ''.join(c for c in str(upc_code) if c.isalnum())

        # Ensure minimum length for Code128
        if len(clean_upc) < 3:
            clean_upc = "123456789012"

        # Create Code128 barcode
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(clean_upc, writer=ImageWriter())

        # Generate image
        buffer = BytesIO()
        barcode_instance.write(buffer)
        buffer.seek(0)

        # Open and resize image
        img = Image.open(buffer)
        img = img.resize((width, height), Image.Resampling.LANCZOS)

        return img

    except Exception as e:
        logger.error(f"Error generating barcode: {str(e)}")
        # Return a placeholder image instead of None
        try:
            placeholder_img = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(placeholder_img)
            draw.rectangle([0, 0, width-1, height-1], outline='black', width=2)
            draw.text((10, height//2-10), "BARCODE ERROR", fill='red')
            return placeholder_img
        except:
            return None

class LabelElement:
    """Base class for label elements (text, barcode, image)."""
    
    def __init__(self, element_type, x=50, y=50, width=100, height=30):
        self.element_type = element_type
        self.x = x
        self.y = y
        self.width = width
        self.height = height
        self.data_column = None  # For dynamic data linking
        self.properties = {}
        
    def to_dict(self):
        """Convert element to dictionary for JSON serialization."""
        return {
            'element_type': self.element_type,
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'data_column': self.data_column,
            'properties': self.properties
        }
    
    @classmethod
    def from_dict(cls, data):
        """Create element from dictionary."""
        element = cls(
            data['element_type'],
            data['x'],
            data['y'],
            data['width'],
            data['height']
        )
        element.data_column = data.get('data_column')
        element.properties = data.get('properties', {})
        return element

class TextElement(LabelElement):
    """Enhanced text element for labels with advanced formatting."""

    def __init__(self, x=50, y=50, width=100, height=30):
        super().__init__('text', x, y, width, height)
        self.properties = {
            'text': 'Sample Text',
            'font_size': 12,
            'font_family': 'Arial',
            'bold': False,
            'italic': False,
            'underline': False,
            'color': '#000000',
            'background_color': 'transparent',
            'text_align': 'left',
            'vertical_align': 'middle',
            'rotation': 0,
            'shadow': False,
            'shadow_color': '#808080',
            'shadow_offset_x': 2,
            'shadow_offset_y': 2,
            'outline': False,
            'outline_color': '#000000',
            'outline_width': 1,
            'text_wrap': False,
            'line_height': 1.2,
            'letter_spacing': 0
        }

class BarcodeElement(LabelElement):
    """Enhanced barcode element for labels with multiple formats."""

    def __init__(self, x=50, y=50, width=150, height=50):
        super().__init__('barcode', x, y, width, height)
        self.properties = {
            'barcode_type': 'code128',
            'show_text': True,
            'text_position': 'bottom',
            'text_size': 10,
            'text_color': '#000000',
            'background_color': '#FFFFFF',
            'bar_color': '#000000',
            'quiet_zone': True,
            'module_width': 0.2,
            'module_height': 15.0,
            'text_distance': 5.0,
            'center_text': True,
            'font_size': 10,
            'human_readable': True
        }

class ImageElement(LabelElement):
    """Enhanced image element for labels with advanced features."""

    def __init__(self, x=50, y=50, width=100, height=100):
        super().__init__('image', x, y, width, height)
        self.properties = {
            'image_data': None,
            'maintain_aspect': True,
            'opacity': 1.0,
            'rotation': 0,
            'flip_horizontal': False,
            'flip_vertical': False,
            'brightness': 1.0,
            'contrast': 1.0,
            'saturation': 1.0,
            'blur': 0,
            'border_width': 0,
            'border_color': '#000000',
            'border_style': 'solid',
            'shadow': False,
            'shadow_color': '#808080',
            'shadow_offset_x': 5,
            'shadow_offset_y': 5,
            'shadow_blur': 5
        }

class ShapeElement(LabelElement):
    """Shape element for geometric shapes and lines."""

    def __init__(self, x=50, y=50, width=100, height=100):
        super().__init__('shape', x, y, width, height)
        self.properties = {
            'shape_type': 'rectangle',  # rectangle, circle, line, arrow
            'fill_color': '#FFFFFF',
            'stroke_color': '#000000',
            'stroke_width': 2,
            'stroke_style': 'solid',  # solid, dashed, dotted
            'opacity': 1.0,
            'rotation': 0,
            'corner_radius': 0,  # for rounded rectangles
            'arrow_head_size': 10,  # for arrows
            'shadow': False,
            'shadow_color': '#808080',
            'shadow_offset_x': 3,
            'shadow_offset_y': 3,
            'shadow_blur': 3
        }

class LineElement(LabelElement):
    """Line element for drawing lines and dividers."""

    def __init__(self, x=50, y=50, width=100, height=2):
        super().__init__('line', x, y, width, height)
        self.properties = {
            'line_style': 'solid',  # solid, dashed, dotted
            'line_width': 2,
            'line_color': '#000000',
            'start_cap': 'none',  # none, arrow, circle, square
            'end_cap': 'none',
            'opacity': 1.0
        }

def initialize_session_state():
    """Initialize session state variables."""
    if 'step' not in st.session_state:
        st.session_state.step = 1
    if 'data_source' not in st.session_state:
        st.session_state.data_source = None
    if 'label_elements' not in st.session_state:
        st.session_state.label_elements = []
    if 'selected_element' not in st.session_state:
        st.session_state.selected_element = None
    if 'label_width_mm' not in st.session_state:
        st.session_state.label_width_mm = 100
    if 'label_height_mm' not in st.session_state:
        st.session_state.label_height_mm = 50
    if 'canvas_width' not in st.session_state:
        st.session_state.canvas_width = 600
    if 'canvas_height' not in st.session_state:
        st.session_state.canvas_height = 300
    # Enhanced design features
    if 'canvas_zoom' not in st.session_state:
        st.session_state.canvas_zoom = 1.0
    if 'show_grid' not in st.session_state:
        st.session_state.show_grid = True
    if 'snap_to_grid' not in st.session_state:
        st.session_state.snap_to_grid = True
    if 'grid_size' not in st.session_state:
        st.session_state.grid_size = 10
    if 'design_history' not in st.session_state:
        st.session_state.design_history = []
    if 'history_index' not in st.session_state:
        st.session_state.history_index = -1
    if 'clipboard' not in st.session_state:
        st.session_state.clipboard = None

def step_1_data_connection():
    """Step 1: Select Data File - Single file upload."""
    st.markdown('<div class="step-indicator">📁 Step 1: Select Data File</div>', unsafe_allow_html=True)

    st.markdown("""
    <div class="upload-section">
        <h3>📁 Upload Your Data File</h3>
        <p>Upload a single data file containing the information you want to use for label creation.</p>
        <p><strong>💡 Supported formats:</strong> CSV, Excel (.xlsx, .xls), TSV, and TXT files</p>
    </div>
    """, unsafe_allow_html=True)

    # Single file upload
    uploaded_file = st.file_uploader(
        "Choose your data file",
        type=['csv', 'xlsx', 'xls', 'tsv', 'txt'],
        key="data_file",
        help="Upload any structured data file. This will be used as the data source for your labels."
    )

    if uploaded_file:
        st.success(f"✅ File uploaded successfully: {uploaded_file.name}")

        # Show file information
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("File Name", uploaded_file.name)
        with col2:
            st.metric("File Size", f"{uploaded_file.size} bytes")
        with col3:
            file_format = detect_file_format(uploaded_file)
            st.metric("File Type", file_format.upper())

        if st.button("📊 Load Data File", use_container_width=True, type="primary"):
            with st.spinner("Loading data file..."):
                try:
                    data_df = load_data_file(uploaded_file)
                    st.session_state.data_source = data_df

                    st.success(f"✅ Data loaded successfully! {data_df.shape[0]} rows, {data_df.shape[1]} columns")

                    # Display first 10 rows
                    st.subheader("📋 Data Preview (First 10 rows)")
                    preview_df = data_df.head(10).to_pandas()
                    st.dataframe(preview_df, use_container_width=True)

                    # Show available columns
                    st.info(f"**Available columns:** {', '.join(data_df.columns)}")

                except Exception as e:
                    st.error(f"❌ Error loading file: {str(e)}")

                    # Show helpful troubleshooting
                    with st.expander("🔧 Troubleshooting Help"):
                        st.markdown("""
                        **Common issues and solutions:**

                        1. **File format issues**: Try converting Excel files to CSV format
                        2. **Encoding problems**: Ensure your file uses UTF-8 encoding
                        3. **Large files**: Very large files may take longer to process
                        4. **Corrupted files**: Try re-saving your file in a different format

                        **File requirements:**
                        - File must have column headers in the first row
                        - Data should be in a tabular format
                        - Avoid merged cells or complex formatting in Excel files
                        """)

                    logger.error(f"Error in step 1: {str(e)}")

    # Show proceed button if data is loaded (outside the load button conditional)
    if st.session_state.data_source is not None:
        st.markdown("---")
        st.success("🎉 **Data loaded successfully!** You can now proceed to the Label Design Studio.")
        if st.button("➡️ Proceed to Label Design Studio", use_container_width=True, type="secondary"):
            st.session_state.step = 2
            st.rerun()

def render_label_elements_to_image(width, height, elements, selected_element_idx=None):
    """Render label elements to a PIL Image for canvas display."""
    try:
        # Create a new image with white background
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)

        # Draw grid if enabled
        if st.session_state.get('show_grid', True):
            grid_size = st.session_state.get('grid_size', 10)
            grid_color = '#E0E0E0'

            # Draw vertical lines
            for x in range(0, width, grid_size):
                draw.line([(x, 0), (x, height)], fill=grid_color, width=1)

            # Draw horizontal lines
            for y in range(0, height, grid_size):
                draw.line([(0, y), (width, y)], fill=grid_color, width=1)

        # Render each element
        for idx, element in enumerate(elements):
            try:
                # Determine if this element is selected
                is_selected = (selected_element_idx == idx)

                # Get element bounds
                x, y, w, h = element.x, element.y, element.width, element.height

                # Render based on element type
                if element.element_type == 'text':
                    render_text_element(draw, element, x, y, w, h, is_selected)
                elif element.element_type == 'barcode':
                    render_barcode_element(draw, element, x, y, w, h, is_selected)
                elif element.element_type == 'image':
                    render_image_element(draw, element, x, y, w, h, is_selected)
                elif element.element_type == 'shape':
                    render_shape_element(draw, element, x, y, w, h, is_selected)
                elif element.element_type == 'line':
                    render_line_element(draw, element, x, y, w, h, is_selected)

                # Draw selection indicator
                if is_selected:
                    # Draw selection border
                    draw.rectangle([x-2, y-2, x+w+2, y+h+2], outline='#007ACC', width=2)
                    # Draw corner handles
                    handle_size = 6
                    handle_color = '#007ACC'
                    corners = [
                        (x-handle_size//2, y-handle_size//2),
                        (x+w-handle_size//2, y-handle_size//2),
                        (x-handle_size//2, y+h-handle_size//2),
                        (x+w-handle_size//2, y+h-handle_size//2)
                    ]
                    for corner_x, corner_y in corners:
                        draw.rectangle([corner_x, corner_y, corner_x+handle_size, corner_y+handle_size],
                                     fill=handle_color, outline=handle_color)

            except Exception as e:
                logger.warning(f"Error rendering element {idx}: {e}")
                # Draw error placeholder
                draw.rectangle([x, y, x+w, y+h], outline='red', width=2)
                draw.text((x+5, y+5), f"Error: {element.element_type}", fill='red')

        return img

    except Exception as e:
        logger.error(f"Error rendering label elements: {e}")
        # Return a simple error image
        img = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((10, 10), f"Render Error: {str(e)}", fill='red')
        return img

def render_text_element(draw, element, x, y, w, h, is_selected):
    """Render a text element."""
    try:
        text = element.properties.get('text', 'Sample Text')
        font_size = element.properties.get('font_size', 12)
        color = element.properties.get('color', '#000000')
        bg_color = element.properties.get('background_color', 'transparent')

        # Draw background if not transparent
        if bg_color != 'transparent':
            draw.rectangle([x, y, x+w, y+h], fill=bg_color)

        # Try to load font (fallback to default if not available)
        try:
            # Try different font paths for cross-platform compatibility
            font_paths = [
                "arial.ttf",  # Windows
                "/System/Library/Fonts/Arial.ttf",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
                "/usr/share/fonts/TTF/arial.ttf"  # Some Linux distributions
            ]
            font = None
            for font_path in font_paths:
                try:
                    font = ImageFont.truetype(font_path, font_size)
                    break
                except:
                    continue
            if font is None:
                font = ImageFont.load_default()
        except Exception as e:
            logger.warning(f"Font loading error: {e}")
            font = ImageFont.load_default()

        # Draw text
        draw.text((x+5, y+5), text, fill=color, font=font)

        # Draw element border
        border_color = '#CCCCCC' if not is_selected else '#007ACC'
        draw.rectangle([x, y, x+w, y+h], outline=border_color, width=1)

    except Exception as e:
        logger.warning(f"Error rendering text element: {e}")
        draw.rectangle([x, y, x+w, y+h], outline='red', width=1)
        draw.text((x+2, y+2), "Text Error", fill='red')

def render_barcode_element(draw, element, x, y, w, h, is_selected):
    """Render a barcode element."""
    try:
        # Draw placeholder barcode
        draw.rectangle([x, y, x+w, y+h], fill='white', outline='black', width=1)

        # Draw barcode bars (simplified representation)
        bar_width = 2
        for i in range(0, w-10, bar_width*2):
            if i % 4 == 0:  # Simple pattern
                draw.rectangle([x+5+i, y+5, x+5+i+bar_width, y+h-15], fill='black')

        # Draw text if enabled
        if element.properties.get('show_text', True):
            text = element.data_column or "123456789"
            try:
                # Try different font paths for cross-platform compatibility
                font_paths = ["arial.ttf", "/System/Library/Fonts/Arial.ttf",
                             "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"]
                font = None
                for font_path in font_paths:
                    try:
                        font = ImageFont.truetype(font_path, 10)
                        break
                    except:
                        continue
                if font is None:
                    font = ImageFont.load_default()
            except:
                font = ImageFont.load_default()
            draw.text((x+5, y+h-12), text, fill='black', font=font)

        # Draw element border
        border_color = '#CCCCCC' if not is_selected else '#007ACC'
        draw.rectangle([x, y, x+w, y+h], outline=border_color, width=1)

    except Exception as e:
        logger.warning(f"Error rendering barcode element: {e}")
        draw.rectangle([x, y, x+w, y+h], outline='red', width=1)
        draw.text((x+2, y+2), "Barcode Error", fill='red')

def render_image_element(draw, element, x, y, w, h, is_selected):
    """Render an image element."""
    try:
        # Draw placeholder for image
        draw.rectangle([x, y, x+w, y+h], fill='#F0F0F0', outline='black', width=1)

        # Draw image icon
        center_x, center_y = x + w//2, y + h//2
        draw.text((center_x-20, center_y-5), "🖼️ Image", fill='gray')

        # Draw element border
        border_color = '#CCCCCC' if not is_selected else '#007ACC'
        draw.rectangle([x, y, x+w, y+h], outline=border_color, width=1)

    except Exception as e:
        logger.warning(f"Error rendering image element: {e}")
        draw.rectangle([x, y, x+w, y+h], outline='red', width=1)
        draw.text((x+2, y+2), "Image Error", fill='red')

def render_shape_element(draw, element, x, y, w, h, is_selected):
    """Render a shape element."""
    try:
        shape_type = element.properties.get('shape_type', 'rectangle')
        fill_color = element.properties.get('fill_color', '#FFFFFF')
        stroke_color = element.properties.get('stroke_color', '#000000')
        stroke_width = element.properties.get('stroke_width', 2)

        if shape_type == 'rectangle':
            draw.rectangle([x, y, x+w, y+h], fill=fill_color, outline=stroke_color, width=stroke_width)
        elif shape_type == 'circle':
            draw.ellipse([x, y, x+w, y+h], fill=fill_color, outline=stroke_color, width=stroke_width)
        elif shape_type == 'arrow':
            # Simple arrow shape
            points = [(x, y+h//2), (x+w-10, y+h//2), (x+w-10, y), (x+w, y+h//2), (x+w-10, y+h), (x+w-10, y+h//2)]
            draw.polygon(points, fill=fill_color, outline=stroke_color)

    except Exception as e:
        logger.warning(f"Error rendering shape element: {e}")
        draw.rectangle([x, y, x+w, y+h], outline='red', width=1)

def render_line_element(draw, element, x, y, w, h, is_selected):
    """Render a line element."""
    try:
        line_color = element.properties.get('line_color', '#000000')
        line_width = element.properties.get('line_width', 2)

        # Draw line from start to end
        draw.line([(x, y+h//2), (x+w, y+h//2)], fill=line_color, width=line_width)

    except Exception as e:
        logger.warning(f"Error rendering line element: {e}")
        draw.line([(x, y), (x+w, y+h)], fill='red', width=2)

def step_2_label_design_studio():
    """Step 2: The Label Design Studio - Interactive canvas and design tools."""
    st.markdown('<div class="step-indicator">🎨 Step 2: Label Design Studio</div>', unsafe_allow_html=True)

    # Two-column layout
    col_canvas, col_tools = st.columns([2, 1])

    with col_canvas:
        st.markdown('<div class="canvas-container">', unsafe_allow_html=True)
        st.subheader("📐 Label Canvas")

        # Label size definition
        size_col1, size_col2 = st.columns(2)
        with size_col1:
            label_width = st.number_input(
                "Width (mm)",
                min_value=10,
                max_value=300,
                value=st.session_state.label_width_mm,
                key="label_width"
            )
        with size_col2:
            label_height = st.number_input(
                "Height (mm)",
                min_value=10,
                max_value=300,
                value=st.session_state.label_height_mm,
                key="label_height"
            )

        # Update session state
        st.session_state.label_width_mm = label_width
        st.session_state.label_height_mm = label_height

        # Calculate canvas dimensions (scale to fit screen)
        aspect_ratio = label_width / label_height
        canvas_width = min(600, int(400 * aspect_ratio))
        canvas_height = int(canvas_width / aspect_ratio)

        st.session_state.canvas_width = canvas_width
        st.session_state.canvas_height = canvas_height

        # Render label elements to image
        try:
            selected_idx = st.session_state.get('selected_element', None)
            background_img = render_label_elements_to_image(
                canvas_width,
                canvas_height,
                st.session_state.label_elements,
                selected_idx
            )

            # Convert PIL image to format suitable for st_canvas
            if background_img is not None:
                img_buffer = io.BytesIO()
                background_img.save(img_buffer, format='PNG')
                img_buffer.seek(0)
            else:
                img_buffer = None

        except Exception as e:
            logger.error(f"Error creating canvas background: {e}")
            background_img = None
            img_buffer = None

        # Interactive Canvas with rendered background
        try:
            # Check if st_canvas is available
            if st_canvas is None:
                raise ImportError("streamlit-drawable-canvas not available")

            canvas_result = st_canvas(
                fill_color="rgba(255, 255, 255, 0.0)",
                stroke_width=2,
                stroke_color="#000000",
                background_color="#FFFFFF",
                background_image=background_img if background_img else None,
                update_streamlit=True,
                width=canvas_width,
                height=canvas_height,
                drawing_mode="transform",
                point_display_radius=3,
                key="label_canvas",
            )
        except Exception as e:
            logger.error(f"Canvas error: {e}")
            st.error("⚠️ Canvas component error. Using fallback display.")
            canvas_result = None
            # Show static preview instead
            if background_img:
                st.image(background_img, caption="Label Preview", width=canvas_width)
            else:
                st.info("📐 Canvas preview will appear here when you add elements to your label.")

        # Handle canvas interactions
        if canvas_result and canvas_result.json_data is not None:
            try:
                objects = canvas_result.json_data.get("objects", [])
                if objects:
                    # Handle element selection and movement
                    for obj in objects:
                        if obj.get("type") == "rect":
                            # Update element position if moved
                            try:
                                element_idx = obj.get("element_idx", 0)
                                if 0 <= element_idx < len(st.session_state.label_elements):
                                    element = st.session_state.label_elements[element_idx]
                                    element.x = obj.get("left", element.x)
                                    element.y = obj.get("top", element.y)
                                    st.rerun()
                            except Exception as e:
                                logger.warning(f"Error updating element position: {e}")
            except Exception as e:
                logger.warning(f"Error processing canvas interactions: {e}")

        st.markdown('</div>', unsafe_allow_html=True)

        # Display canvas info
        st.info(f"📏 Label Size: {label_width}mm × {label_height}mm | Canvas: {canvas_width}px × {canvas_height}px")

    with col_tools:
        st.markdown('<div class="toolbox">', unsafe_allow_html=True)
        st.subheader("🧰 Toolbox")

        # Enhanced toolbox with more options
        st.subheader("🎨 Design Elements")

        # Text and Data Elements
        col_text1, col_text2 = st.columns(2)
        with col_text1:
            if st.button("📝 Add Text", use_container_width=True):
                add_text_element()
        with col_text2:
            if st.button("📊 Add Barcode", use_container_width=True):
                add_barcode_element()

        # Media Elements
        col_media1, col_media2 = st.columns(2)
        with col_media1:
            if st.button("🖼️ Add Image", use_container_width=True):
                add_image_element()
        with col_media2:
            if st.button("🔗 Add QR Code", use_container_width=True):
                add_qr_element()

        # Shape Elements
        st.subheader("📐 Shapes & Lines")
        col_shape1, col_shape2 = st.columns(2)
        with col_shape1:
            if st.button("⬜ Add Rectangle", use_container_width=True):
                add_shape_element('rectangle')
        with col_shape2:
            if st.button("⭕ Add Circle", use_container_width=True):
                add_shape_element('circle')

        col_line1, col_line2 = st.columns(2)
        with col_line1:
            if st.button("📏 Add Line", use_container_width=True):
                add_line_element()
        with col_line2:
            if st.button("➡️ Add Arrow", use_container_width=True):
                add_shape_element('arrow')

        # Canvas Controls
        st.subheader("🔧 Canvas Controls")

        # Zoom controls
        zoom_col1, zoom_col2, zoom_col3 = st.columns(3)
        with zoom_col1:
            if st.button("🔍+", use_container_width=True, help="Zoom In"):
                st.session_state.canvas_zoom = min(3.0, st.session_state.canvas_zoom * 1.2)
                st.rerun()
        with zoom_col2:
            if st.button("🔍-", use_container_width=True, help="Zoom Out"):
                st.session_state.canvas_zoom = max(0.3, st.session_state.canvas_zoom / 1.2)
                st.rerun()
        with zoom_col3:
            if st.button("🔍=", use_container_width=True, help="Reset Zoom"):
                st.session_state.canvas_zoom = 1.0
                st.rerun()

        # Grid and snap controls
        st.session_state.show_grid = st.checkbox("Show Grid", value=st.session_state.show_grid)
        st.session_state.snap_to_grid = st.checkbox("Snap to Grid", value=st.session_state.snap_to_grid)
        st.session_state.grid_size = st.slider("Grid Size", 5, 50, st.session_state.grid_size)

        st.markdown('</div>', unsafe_allow_html=True)

        # Properties Inspector
        st.markdown('<div class="properties-panel">', unsafe_allow_html=True)
        st.subheader("🔧 Properties Inspector")

        if st.session_state.selected_element is not None:
            show_element_properties()
        else:
            st.info("Click on an element on the canvas to edit its properties.")

        st.markdown('</div>', unsafe_allow_html=True)

        # Element list
        if st.session_state.label_elements:
            st.subheader("📋 Elements")
            for i, element in enumerate(st.session_state.label_elements):
                element_name = f"{element.element_type.title()} {i+1}"
                if st.button(element_name, key=f"element_{i}"):
                    st.session_state.selected_element = i
                    st.rerun()

    # Navigation buttons
    st.markdown("---")
    nav_col1, nav_col2 = st.columns(2)

    with nav_col1:
        if st.button("⬅️ Back to Step 1: Select Data File", use_container_width=True):
            st.session_state.step = 1
            st.rerun()

    with nav_col2:
        if st.session_state.label_elements and st.button("➡️ Step 3: Generate PDF", use_container_width=True):
            st.session_state.step = 3
            st.rerun()

def add_text_element():
    """Add a new text element to the label."""
    try:
        text_element = TextElement()
        st.session_state.label_elements.append(text_element)
        st.session_state.selected_element = len(st.session_state.label_elements) - 1
        st.rerun()
    except Exception as e:
        logger.error(f"Error adding text element: {e}")
        st.error(f"Error adding text element: {str(e)}")

def add_barcode_element():
    """Add a new barcode element to the label."""
    try:
        barcode_element = BarcodeElement()
        # Set default data column to UPC if available, otherwise first column
        if st.session_state.data_source is not None:
            try:
                columns = st.session_state.data_source.columns
                if 'UPC' in columns:
                    barcode_element.data_column = 'UPC'
                elif len(columns) > 0:
                    barcode_element.data_column = columns[0]
            except Exception as e:
                logger.warning(f"Could not access data source columns: {e}")

        st.session_state.label_elements.append(barcode_element)
        st.session_state.selected_element = len(st.session_state.label_elements) - 1
        st.rerun()
    except Exception as e:
        logger.error(f"Error adding barcode element: {e}")
        st.error(f"Error adding barcode element: {str(e)}")

def add_image_element():
    """Add a new image element to the label."""
    try:
        image_element = ImageElement()
        st.session_state.label_elements.append(image_element)
        st.session_state.selected_element = len(st.session_state.label_elements) - 1
        st.rerun()
    except Exception as e:
        logger.error(f"Error adding image element: {e}")
        st.error(f"Error adding image element: {str(e)}")

def add_qr_element():
    """Add a new QR code element to the label."""
    try:
        qr_element = BarcodeElement()
        qr_element.properties['barcode_type'] = 'qr'
        qr_element.properties['show_text'] = False
        # Set default data column if available
        if st.session_state.data_source is not None:
            try:
                columns = st.session_state.data_source.columns
                if len(columns) > 0:
                    qr_element.data_column = columns[0]
            except Exception as e:
                logger.warning(f"Could not access data source columns: {e}")

        st.session_state.label_elements.append(qr_element)
        st.session_state.selected_element = len(st.session_state.label_elements) - 1
        st.rerun()
    except Exception as e:
        logger.error(f"Error adding QR element: {e}")
        st.error(f"Error adding QR element: {str(e)}")

def add_shape_element(shape_type):
    """Add a new shape element to the label."""
    try:
        shape_element = ShapeElement()
        shape_element.properties['shape_type'] = shape_type
        st.session_state.label_elements.append(shape_element)
        st.session_state.selected_element = len(st.session_state.label_elements) - 1
        st.rerun()
    except Exception as e:
        logger.error(f"Error adding shape element: {e}")
        st.error(f"Error adding shape element: {str(e)}")

def add_line_element():
    """Add a new line element to the label."""
    try:
        line_element = LineElement()
        st.session_state.label_elements.append(line_element)
        st.session_state.selected_element = len(st.session_state.label_elements) - 1
        st.rerun()
    except Exception as e:
        logger.error(f"Error adding line element: {e}")
        st.error(f"Error adding line element: {str(e)}")

def show_element_properties():
    """Show properties panel for selected element."""
    if st.session_state.selected_element is None:
        return

    element_idx = st.session_state.selected_element
    if element_idx >= len(st.session_state.label_elements):
        return

    element = st.session_state.label_elements[element_idx]

    st.write(f"**Selected:** {element.element_type.title()} Element")

    # Data linking dropdown (for text and barcode elements)
    if element.element_type in ['text', 'barcode']:
        if st.session_state.data_source is not None:
            try:
                # Safely get columns from data source
                if hasattr(st.session_state.data_source, 'columns'):
                    data_columns = list(st.session_state.data_source.columns)
                else:
                    data_columns = []

                columns = ['[Static Text]'] + data_columns
                current_column = element.data_column if element.data_column else '[Static Text]'

                # Ensure current column exists in options
                if current_column not in columns and current_column is not None:
                    columns.append(current_column)

                selected_column = st.selectbox(
                    "Link to Data Column:",
                    options=columns,
                    index=columns.index(current_column) if current_column in columns else 0,
                    key=f"data_link_{element_idx}"
                )

                if selected_column != '[Static Text]':
                    element.data_column = selected_column
                else:
                    element.data_column = None
            except Exception as e:
                logger.warning(f"Could not access data columns: {e}")
                st.warning("⚠️ Could not access data columns for linking.")
                # Provide manual input as fallback
                element.data_column = st.text_input(
                    "Manual Column Name:",
                    value=element.data_column or "",
                    key=f"manual_column_{element_idx}"
                )

    # Element-specific properties
    if element.element_type == 'text':
        show_text_properties(element, element_idx)
    elif element.element_type == 'barcode':
        show_barcode_properties(element, element_idx)
    elif element.element_type == 'image':
        show_image_properties(element, element_idx)
    elif element.element_type == 'shape':
        show_shape_properties(element, element_idx)
    elif element.element_type == 'line':
        show_line_properties(element, element_idx)

    # Position and size properties
    st.subheader("📐 Position & Size")

    col1, col2 = st.columns(2)
    with col1:
        element.x = st.number_input("X Position", value=element.x, key=f"x_{element_idx}")
        element.width = st.number_input("Width", value=element.width, min_value=10, key=f"width_{element_idx}")

    with col2:
        element.y = st.number_input("Y Position", value=element.y, key=f"y_{element_idx}")
        element.height = st.number_input("Height", value=element.height, min_value=10, key=f"height_{element_idx}")

    # Delete button
    if st.button("🗑️ Delete Element", use_container_width=True, type="secondary"):
        st.session_state.label_elements.pop(element_idx)
        st.session_state.selected_element = None
        st.rerun()

def show_text_properties(element, element_idx):
    """Show enhanced properties specific to text elements."""
    st.subheader("📝 Text Properties")

    # Text value (disabled if linked to data)
    if element.data_column:
        st.text_input("Text Value", value=f"[Linked to {element.data_column}]", disabled=True)
    else:
        element.properties['text'] = st.text_area(
            "Text Value",
            value=element.properties.get('text', 'Sample Text'),
            height=100,
            key=f"text_value_{element_idx}"
        )

    # Font properties
    col1, col2 = st.columns(2)
    with col1:
        element.properties['font_size'] = st.slider(
            "Font Size",
            min_value=6,
            max_value=144,
            value=element.properties.get('font_size', 12),
            key=f"font_size_{element_idx}"
        )

    with col2:
        element.properties['font_family'] = st.selectbox(
            "Font Family",
            options=['Arial', 'Helvetica', 'Times New Roman', 'Calibri', 'Courier', 'Verdana', 'Georgia', 'Impact'],
            index=['Arial', 'Helvetica', 'Times New Roman', 'Calibri', 'Courier', 'Verdana', 'Georgia', 'Impact'].index(
                element.properties.get('font_family', 'Arial')
            ) if element.properties.get('font_family', 'Arial') in ['Arial', 'Helvetica', 'Times New Roman', 'Calibri', 'Courier', 'Verdana', 'Georgia', 'Impact'] else 0,
            key=f"font_family_{element_idx}"
        )

    # Style options
    col1, col2, col3 = st.columns(3)
    with col1:
        element.properties['bold'] = st.checkbox(
            "Bold",
            value=element.properties.get('bold', False),
            key=f"bold_{element_idx}"
        )

    with col2:
        element.properties['italic'] = st.checkbox(
            "Italic",
            value=element.properties.get('italic', False),
            key=f"italic_{element_idx}"
        )

    with col3:
        element.properties['underline'] = st.checkbox(
            "Underline",
            value=element.properties.get('underline', False),
            key=f"underline_{element_idx}"
        )

    # Colors
    col1, col2 = st.columns(2)
    with col1:
        element.properties['color'] = st.color_picker(
            "Text Color",
            value=element.properties.get('color', '#000000'),
            key=f"color_{element_idx}"
        )

    with col2:
        bg_color = element.properties.get('background_color', 'transparent')
        if bg_color == 'transparent':
            bg_color = '#FFFFFF'
        element.properties['background_color'] = st.color_picker(
            "Background Color",
            value=bg_color,
            key=f"bg_color_{element_idx}"
        )

    # Alignment
    col1, col2 = st.columns(2)
    with col1:
        element.properties['text_align'] = st.selectbox(
            "Text Alignment",
            options=['left', 'center', 'right', 'justify'],
            index=['left', 'center', 'right', 'justify'].index(element.properties.get('text_align', 'left')),
            key=f"text_align_{element_idx}"
        )

    with col2:
        element.properties['vertical_align'] = st.selectbox(
            "Vertical Alignment",
            options=['top', 'middle', 'bottom'],
            index=['top', 'middle', 'bottom'].index(element.properties.get('vertical_align', 'middle')),
            key=f"vertical_align_{element_idx}"
        )

    # Advanced text properties
    with st.expander("🎨 Advanced Text Effects"):
        # Rotation
        element.properties['rotation'] = st.slider(
            "Rotation (degrees)",
            min_value=-180,
            max_value=180,
            value=element.properties.get('rotation', 0),
            key=f"rotation_{element_idx}"
        )

        # Shadow
        element.properties['shadow'] = st.checkbox(
            "Drop Shadow",
            value=element.properties.get('shadow', False),
            key=f"shadow_{element_idx}"
        )

        if element.properties['shadow']:
            col1, col2 = st.columns(2)
            with col1:
                element.properties['shadow_color'] = st.color_picker(
                    "Shadow Color",
                    value=element.properties.get('shadow_color', '#808080'),
                    key=f"shadow_color_{element_idx}"
                )
            with col2:
                element.properties['shadow_offset_x'] = st.number_input(
                    "Shadow X Offset",
                    value=element.properties.get('shadow_offset_x', 2),
                    key=f"shadow_x_{element_idx}"
                )
                element.properties['shadow_offset_y'] = st.number_input(
                    "Shadow Y Offset",
                    value=element.properties.get('shadow_offset_y', 2),
                    key=f"shadow_y_{element_idx}"
                )

        # Outline
        element.properties['outline'] = st.checkbox(
            "Text Outline",
            value=element.properties.get('outline', False),
            key=f"outline_{element_idx}"
        )

        if element.properties['outline']:
            col1, col2 = st.columns(2)
            with col1:
                element.properties['outline_color'] = st.color_picker(
                    "Outline Color",
                    value=element.properties.get('outline_color', '#000000'),
                    key=f"outline_color_{element_idx}"
                )
            with col2:
                element.properties['outline_width'] = st.slider(
                    "Outline Width",
                    min_value=1,
                    max_value=10,
                    value=element.properties.get('outline_width', 1),
                    key=f"outline_width_{element_idx}"
                )

        # Text spacing
        col1, col2 = st.columns(2)
        with col1:
            element.properties['line_height'] = st.slider(
                "Line Height",
                min_value=0.5,
                max_value=3.0,
                value=element.properties.get('line_height', 1.2),
                step=0.1,
                key=f"line_height_{element_idx}"
            )
        with col2:
            element.properties['letter_spacing'] = st.slider(
                "Letter Spacing",
                min_value=-5,
                max_value=20,
                value=element.properties.get('letter_spacing', 0),
                key=f"letter_spacing_{element_idx}"
            )

def show_barcode_properties(element, element_idx):
    """Show enhanced properties specific to barcode elements."""
    st.subheader("📊 Barcode Properties")

    if element.data_column:
        st.success(f"✅ Linked to: {element.data_column}")
    else:
        st.warning("⚠️ No data column linked. Barcode will show sample data.")

    # Barcode type selection
    barcode_types = ['code128', 'code39', 'ean13', 'ean8', 'upca', 'qr', 'datamatrix']
    current_type = element.properties.get('barcode_type', 'code128')
    element.properties['barcode_type'] = st.selectbox(
        "Barcode Type",
        options=barcode_types,
        index=barcode_types.index(current_type) if current_type in barcode_types else 0,
        key=f"barcode_type_{element_idx}"
    )

    # Text display options
    col1, col2 = st.columns(2)
    with col1:
        element.properties['show_text'] = st.checkbox(
            "Show Human Readable Text",
            value=element.properties.get('show_text', True),
            key=f"show_text_{element_idx}"
        )

    with col2:
        if element.properties['show_text']:
            element.properties['text_position'] = st.selectbox(
                "Text Position",
                options=['bottom', 'top'],
                index=['bottom', 'top'].index(element.properties.get('text_position', 'bottom')),
                key=f"text_position_{element_idx}"
            )

    # Colors
    col1, col2 = st.columns(2)
    with col1:
        element.properties['bar_color'] = st.color_picker(
            "Bar Color",
            value=element.properties.get('bar_color', '#000000'),
            key=f"bar_color_{element_idx}"
        )

    with col2:
        element.properties['background_color'] = st.color_picker(
            "Background Color",
            value=element.properties.get('background_color', '#FFFFFF'),
            key=f"barcode_bg_color_{element_idx}"
        )

    # Advanced barcode settings
    with st.expander("🔧 Advanced Barcode Settings"):
        if element.properties['show_text']:
            col1, col2 = st.columns(2)
            with col1:
                element.properties['text_size'] = st.slider(
                    "Text Size",
                    min_value=6,
                    max_value=24,
                    value=element.properties.get('text_size', 10),
                    key=f"barcode_text_size_{element_idx}"
                )
            with col2:
                element.properties['text_color'] = st.color_picker(
                    "Text Color",
                    value=element.properties.get('text_color', '#000000'),
                    key=f"barcode_text_color_{element_idx}"
                )

        # Module settings for linear barcodes
        if element.properties['barcode_type'] not in ['qr', 'datamatrix']:
            col1, col2 = st.columns(2)
            with col1:
                element.properties['module_width'] = st.slider(
                    "Module Width",
                    min_value=0.1,
                    max_value=2.0,
                    value=element.properties.get('module_width', 0.2),
                    step=0.1,
                    key=f"module_width_{element_idx}"
                )
            with col2:
                element.properties['module_height'] = st.slider(
                    "Module Height",
                    min_value=5.0,
                    max_value=50.0,
                    value=element.properties.get('module_height', 15.0),
                    step=1.0,
                    key=f"module_height_{element_idx}"
                )

        # Quiet zone
        element.properties['quiet_zone'] = st.checkbox(
            "Include Quiet Zone",
            value=element.properties.get('quiet_zone', True),
            key=f"quiet_zone_{element_idx}"
        )

def show_image_properties(element, element_idx):
    """Show properties specific to image elements."""
    st.subheader("🖼️ Image Properties")

    uploaded_image = st.file_uploader(
        "Upload Image",
        type=['png', 'jpg', 'jpeg', 'gif'],
        key=f"image_upload_{element_idx}"
    )

    if uploaded_image:
        try:
            # Convert to base64 for storage
            uploaded_image.seek(0)  # Reset file pointer
            image_bytes = uploaded_image.read()
            image_b64 = base64.b64encode(image_bytes).decode()
            element.properties['image_data'] = image_b64

            # Show preview
            uploaded_image.seek(0)  # Reset for preview
            st.image(uploaded_image, caption="Image Preview", width=150)
            st.success("✅ Image uploaded successfully")
        except Exception as e:
            st.error(f"❌ Error uploading image: {str(e)}")
            logger.error(f"Image upload error: {e}")

    element.properties['maintain_aspect'] = st.checkbox(
        "Maintain Aspect Ratio",
        value=element.properties.get('maintain_aspect', True),
        key=f"maintain_aspect_{element_idx}"
    )

def show_shape_properties(element, element_idx):
    """Show properties specific to shape elements."""
    st.subheader("📐 Shape Properties")

    # Shape type
    shape_types = ['rectangle', 'circle', 'arrow']
    current_shape = element.properties.get('shape_type', 'rectangle')
    element.properties['shape_type'] = st.selectbox(
        "Shape Type",
        options=shape_types,
        index=shape_types.index(current_shape) if current_shape in shape_types else 0,
        key=f"shape_type_{element_idx}"
    )

    # Colors
    col1, col2 = st.columns(2)
    with col1:
        element.properties['fill_color'] = st.color_picker(
            "Fill Color",
            value=element.properties.get('fill_color', '#FFFFFF'),
            key=f"fill_color_{element_idx}"
        )

    with col2:
        element.properties['stroke_color'] = st.color_picker(
            "Stroke Color",
            value=element.properties.get('stroke_color', '#000000'),
            key=f"stroke_color_{element_idx}"
        )

    # Stroke properties
    col1, col2 = st.columns(2)
    with col1:
        element.properties['stroke_width'] = st.slider(
            "Stroke Width",
            min_value=0,
            max_value=20,
            value=element.properties.get('stroke_width', 2),
            key=f"stroke_width_{element_idx}"
        )

    with col2:
        element.properties['stroke_style'] = st.selectbox(
            "Stroke Style",
            options=['solid', 'dashed', 'dotted'],
            index=['solid', 'dashed', 'dotted'].index(element.properties.get('stroke_style', 'solid')),
            key=f"stroke_style_{element_idx}"
        )

    # Shape-specific properties
    if element.properties['shape_type'] == 'rectangle':
        element.properties['corner_radius'] = st.slider(
            "Corner Radius",
            min_value=0,
            max_value=50,
            value=element.properties.get('corner_radius', 0),
            key=f"corner_radius_{element_idx}"
        )
    elif element.properties['shape_type'] == 'arrow':
        element.properties['arrow_head_size'] = st.slider(
            "Arrow Head Size",
            min_value=5,
            max_value=50,
            value=element.properties.get('arrow_head_size', 10),
            key=f"arrow_head_size_{element_idx}"
        )

    # Advanced properties
    with st.expander("🎨 Advanced Shape Effects"):
        element.properties['opacity'] = st.slider(
            "Opacity",
            min_value=0.0,
            max_value=1.0,
            value=element.properties.get('opacity', 1.0),
            step=0.1,
            key=f"shape_opacity_{element_idx}"
        )

        element.properties['rotation'] = st.slider(
            "Rotation (degrees)",
            min_value=-180,
            max_value=180,
            value=element.properties.get('rotation', 0),
            key=f"shape_rotation_{element_idx}"
        )

        element.properties['shadow'] = st.checkbox(
            "Drop Shadow",
            value=element.properties.get('shadow', False),
            key=f"shape_shadow_{element_idx}"
        )

def show_line_properties(element, element_idx):
    """Show properties specific to line elements."""
    st.subheader("📏 Line Properties")

    # Line style
    col1, col2 = st.columns(2)
    with col1:
        element.properties['line_style'] = st.selectbox(
            "Line Style",
            options=['solid', 'dashed', 'dotted'],
            index=['solid', 'dashed', 'dotted'].index(element.properties.get('line_style', 'solid')),
            key=f"line_style_{element_idx}"
        )

    with col2:
        element.properties['line_width'] = st.slider(
            "Line Width",
            min_value=1,
            max_value=20,
            value=element.properties.get('line_width', 2),
            key=f"line_width_{element_idx}"
        )

    # Line color
    element.properties['line_color'] = st.color_picker(
        "Line Color",
        value=element.properties.get('line_color', '#000000'),
        key=f"line_color_{element_idx}"
    )

    # End caps
    col1, col2 = st.columns(2)
    with col1:
        element.properties['start_cap'] = st.selectbox(
            "Start Cap",
            options=['none', 'arrow', 'circle', 'square'],
            index=['none', 'arrow', 'circle', 'square'].index(element.properties.get('start_cap', 'none')),
            key=f"start_cap_{element_idx}"
        )

    with col2:
        element.properties['end_cap'] = st.selectbox(
            "End Cap",
            options=['none', 'arrow', 'circle', 'square'],
            index=['none', 'arrow', 'circle', 'square'].index(element.properties.get('end_cap', 'none')),
            key=f"end_cap_{element_idx}"
        )

    # Opacity
    element.properties['opacity'] = st.slider(
        "Opacity",
        min_value=0.0,
        max_value=1.0,
        value=element.properties.get('opacity', 1.0),
        step=0.1,
        key=f"line_opacity_{element_idx}"
    )

def step_3_generate_pdf():
    """Step 3: Generate & Download PDF - Batch processing and PDF creation."""
    st.markdown('<div class="step-indicator">📄 Step 3: Generate & Download PDF</div>', unsafe_allow_html=True)

    st.subheader("🖨️ Print Settings")

    col1, col2, col3 = st.columns(3)

    with col1:
        page_size = st.selectbox(
            "Page Size",
            options=["A4", "Letter"],
            index=0
        )

    with col2:
        labels_per_row = st.number_input(
            "Labels per Row",
            min_value=1,
            max_value=10,
            value=2
        )

    with col3:
        labels_per_column = st.number_input(
            "Labels per Column",
            min_value=1,
            max_value=20,
            value=5
        )

    # Show preview information
    try:
        if st.session_state.data_source is not None:
            total_records = len(st.session_state.data_source)
        else:
            total_records = 0
    except Exception as e:
        logger.warning(f"Error getting data source length: {e}")
        total_records = 0

    labels_per_page = labels_per_row * labels_per_column
    total_pages = (total_records + labels_per_page - 1) // labels_per_page if labels_per_page > 0 else 0

    st.info(f"📊 **Summary:** {total_records} labels across {total_pages} pages ({labels_per_page} labels per page)")

    # Generate PDF button
    if st.button("🏷️ Generate PDF", use_container_width=True, type="primary"):
        with st.spinner(f"Generating {total_records} labels..."):
            try:
                pdf_buffer = generate_labels_pdf(
                    st.session_state.data_source,
                    st.session_state.label_elements,
                    st.session_state.label_width_mm,
                    st.session_state.label_height_mm,
                    page_size,
                    labels_per_row,
                    labels_per_column
                )

                if pdf_buffer:
                    st.success(f"✅ {total_records} labels generated successfully!")

                    st.download_button(
                        label="📥 Download Labels PDF",
                        data=pdf_buffer.getvalue(),
                        file_name="labels.pdf",
                        mime="application/pdf",
                        use_container_width=True
                    )
                else:
                    st.error("❌ Failed to generate PDF")

            except Exception as e:
                st.error(f"❌ PDF generation failed: {str(e)}")
                logger.error(f"PDF generation error: {str(e)}")

    # Navigation buttons
    st.markdown("---")
    nav_col1, nav_col2 = st.columns(2)

    with nav_col1:
        if st.button("⬅️ Back to Step 2: Design Studio", use_container_width=True):
            st.session_state.step = 2
            st.rerun()

    with nav_col2:
        if st.button("🔄 Start Over", use_container_width=True):
            # Reset session state
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()

def generate_labels_pdf(data_df, label_elements, label_width_mm, label_height_mm,
                       page_size, labels_per_row, labels_per_column):
    """Generate PDF with all labels using fpdf2."""
    try:
        from fpdf import FPDF

        # Create PDF
        pdf = FPDF()

        # Set page size
        if page_size == "A4":
            page_width, page_height = 210, 297  # A4 in mm
        else:  # Letter
            page_width, page_height = 216, 279  # Letter in mm

        pdf.set_auto_page_break(auto=True, margin=10)

        # Calculate label positions
        margin = 10
        available_width = page_width - (2 * margin)
        available_height = page_height - (2 * margin)

        label_spacing_x = available_width / labels_per_row
        label_spacing_y = available_height / labels_per_column

        # Convert data to pandas for easier iteration
        try:
            if data_df is None:
                # Create a single dummy row for testing
                import pandas as pd
                pandas_df = pd.DataFrame({'Sample': ['Test Data']})
            elif hasattr(data_df, 'to_pandas'):
                pandas_df = data_df.to_pandas()
            else:
                pandas_df = data_df
        except Exception as e:
            logger.error(f"Error converting data: {e}")
            # Fallback to dummy data
            import pandas as pd
            pandas_df = pd.DataFrame({'Sample': ['Test Data']})

        labels_placed = 0

        try:
            for index, row in pandas_df.iterrows():
                try:
                    # Calculate position on page
                    page_position = labels_placed % (labels_per_row * labels_per_column)

                    if page_position == 0:
                        pdf.add_page()

                    # Calculate x, y position for this label
                    row_on_page = page_position // labels_per_row
                    col_on_page = page_position % labels_per_row

                    label_x = margin + (col_on_page * label_spacing_x)
                    label_y = margin + (row_on_page * label_spacing_y)

                    # Render label elements
                    render_label_on_pdf(pdf, label_elements, row, label_x, label_y,
                                       label_width_mm, label_height_mm)

                    labels_placed += 1

                except Exception as e:
                    logger.warning(f"Error processing row {index}: {e}")
                    # Continue with next row
                    continue

        except Exception as e:
            logger.error(f"Error iterating through data: {e}")
            # Add at least one page with sample data
            if labels_placed == 0:
                pdf.add_page()
                render_label_on_pdf(pdf, label_elements, {"Sample": "Data"},
                                   margin, margin, label_width_mm, label_height_mm)

        # Save to buffer
        buffer = BytesIO()
        pdf_bytes = pdf.output()  # fpdf2 returns bytes directly
        buffer.write(pdf_bytes)
        buffer.seek(0)

        return buffer

    except Exception as e:
        logger.error(f"Error generating PDF: {str(e)}")
        return None

def render_label_on_pdf(pdf, label_elements, data_row, label_x, label_y,
                       label_width_mm, label_height_mm):
    """Render a single label on the PDF."""
    try:
        # Draw label border (optional)
        pdf.set_draw_color(200, 200, 200)
        pdf.rect(label_x, label_y, label_width_mm, label_height_mm)

        # Render each element
        for element in label_elements:
            # Calculate element position relative to label
            element_x = label_x + (element.x / 600) * label_width_mm  # Scale from canvas to mm
            element_y = label_y + (element.y / 300) * label_height_mm  # Scale from canvas to mm
            element_width = (element.width / 600) * label_width_mm
            element_height = (element.height / 300) * label_height_mm

            if element.element_type == 'text':
                render_text_element_pdf(pdf, element, data_row, element_x, element_y,
                                      element_width, element_height)
            elif element.element_type == 'barcode':
                render_barcode_element_pdf(pdf, element, data_row, element_x, element_y,
                                         element_width, element_height)
            elif element.element_type == 'image':
                render_image_element_pdf(pdf, element, element_x, element_y,
                                       element_width, element_height)

    except Exception as e:
        logger.error(f"Error rendering label: {str(e)}")

def render_text_element_pdf(pdf, element, data_row, x, y, width, height):
    """Render text element on PDF."""
    try:
        # Get text content
        text = element.properties.get('text', 'Sample Text')
        if element.data_column:
            try:
                if hasattr(data_row, 'get'):
                    # Dictionary-like access
                    text = str(data_row.get(element.data_column, text))
                elif hasattr(data_row, element.data_column):
                    # Attribute access
                    text = str(getattr(data_row, element.data_column, text))
                elif element.data_column in data_row:
                    # Index access
                    text = str(data_row[element.data_column])
                else:
                    # Fallback: try to access as pandas Series
                    try:
                        text = str(data_row.iloc[data_row.index.get_loc(element.data_column)])
                    except:
                        logger.warning(f"Could not access data column {element.data_column}")
                        text = f"[{element.data_column}]"
            except Exception as e:
                logger.warning(f"Could not access data column {element.data_column}: {e}")
                text = f"[{element.data_column}]"

        # Set font
        font_size = element.properties.get('font_size', 12)
        pdf.set_font('Arial', size=font_size)

        # Set text color (simplified - fpdf2 uses RGB 0-255)
        color = element.properties.get('color', '#000000')
        if color.startswith('#'):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            pdf.set_text_color(r, g, b)

        # Position and render text
        pdf.set_xy(x, y)
        pdf.cell(width, height, text, align='C')

    except Exception as e:
        logger.error(f"Error rendering text element: {str(e)}")

def render_barcode_element_pdf(pdf, element, data_row, x, y, width, height):
    """Render barcode element on PDF."""
    try:
        # Get barcode data
        barcode_data = "123456789"  # Default sample barcode
        if element.data_column:
            try:
                if hasattr(data_row, 'get'):
                    # Dictionary-like access
                    barcode_data = str(data_row.get(element.data_column, barcode_data))
                elif hasattr(data_row, element.data_column):
                    # Attribute access
                    barcode_data = str(getattr(data_row, element.data_column, barcode_data))
                elif element.data_column in data_row:
                    # Index access
                    barcode_data = str(data_row[element.data_column])
                else:
                    # Fallback: try to access as pandas Series
                    try:
                        barcode_data = str(data_row.iloc[data_row.index.get_loc(element.data_column)])
                    except:
                        logger.warning(f"Could not access barcode data column {element.data_column}")
                        barcode_data = "123456789"
            except Exception as e:
                logger.warning(f"Could not access data column {element.data_column}: {e}")
                barcode_data = "123456789"

        # Generate barcode image
        barcode_img = generate_barcode_image(barcode_data, int(width*3), int(height*3))

        if barcode_img:
            # Save barcode image to temporary buffer
            img_buffer = BytesIO()
            barcode_img.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # Add image to PDF (fpdf2 method)
            try:
                pdf.image(img_buffer, x, y, width, height)
            except Exception as img_error:
                logger.warning(f"Could not add barcode image to PDF: {img_error}")
                # Draw placeholder rectangle instead
                pdf.set_draw_color(0, 0, 0)
                pdf.rect(x, y, width, height)

    except Exception as e:
        logger.error(f"Error rendering barcode element: {str(e)}")

def render_image_element_pdf(pdf, element, x, y, width, height):
    """Render image element on PDF."""
    try:
        image_data = element.properties.get('image_data')
        if image_data:
            # Decode base64 image
            image_bytes = base64.b64decode(image_data)
            img_buffer = BytesIO(image_bytes)

            # Add image to PDF
            try:
                pdf.image(img_buffer, x, y, width, height)
            except Exception as img_error:
                logger.warning(f"Could not add image to PDF: {img_error}")
                # Draw placeholder rectangle instead
                pdf.set_draw_color(0, 0, 0)
                pdf.rect(x, y, width, height)

    except Exception as e:
        logger.error(f"Error rendering image element: {str(e)}")

def main():
    """Main application function for Data-Driven Label Studio V4."""
    # Page configuration MUST be the first Streamlit command
    st.set_page_config(
        page_title="Data-Driven Label Studio V4 - Professional Interactive Label Design",
        page_icon="🏷️",
        layout="wide",
        initial_sidebar_state="collapsed"
    )

    # Log application start
    logger.info("Data-Driven Label Studio V4 session started.")

    # Initialize session state
    initialize_session_state()

    # Apply custom styling
    apply_custom_css()

    # Main title and subtitle
    st.markdown('<h1 class="main-title">🏷️ Data-Driven Label Studio V4</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle">Professional Interactive Label Design - Like BarTender, but in your browser!</p>', unsafe_allow_html=True)

    # Application overview
    st.markdown("""
    <div class="toolbox">
        <h3>🎯 Simplified Three-Step Workflow:</h3>
        <p><strong>Step 1:</strong> Select Data File → <strong>Step 2:</strong> Visual Label Design Studio → <strong>Step 3:</strong> Batch PDF Generation</p>
        <br>
        <p><strong>🚀 V4 Features:</strong></p>
        <ul>
            <li><strong>Single File Upload:</strong> No complex merging - just upload your data file</li>
            <li><strong>Interactive Canvas:</strong> Drag-and-drop label design with real-time preview</li>
            <li><strong>Dynamic Data Linking:</strong> Connect text and barcodes to your data columns</li>
            <li><strong>Professional Output:</strong> High-quality PDF generation with precise positioning</li>
            <li><strong>Barcode Support:</strong> Automatic Code128 barcode generation from any data column</li>
            <li><strong>Multi-Element Design:</strong> Text, barcodes, and images on the same label</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)

    # Step progress indicator
    step_names = ["📁 Select Data File", "🎨 Label Design Studio", "📄 Generate PDF"]
    current_step = st.session_state.step

    # Create progress indicator
    progress_cols = st.columns(3)
    for i, step_name in enumerate(step_names, 1):
        with progress_cols[i-1]:
            if i == current_step:
                st.markdown(f"**🔵 Step {i}: {step_name}**")
            elif i < current_step:
                st.markdown(f"✅ Step {i}: {step_name}")
            else:
                st.markdown(f"⚪ Step {i}: {step_name}")

    st.markdown("---")

    # Step navigation with automatic progression
    if st.session_state.step == 1:
        step_1_data_connection()
    elif st.session_state.step == 2:
        if st.session_state.data_source is not None:
            step_2_label_design_studio()
        else:
            st.error("❌ No data loaded. Please go back to Step 1.")
            if st.button("⬅️ Back to Step 1"):
                st.session_state.step = 1
                st.rerun()
    elif st.session_state.step == 3:
        if st.session_state.label_elements:
            step_3_generate_pdf()
        else:
            st.error("❌ No label elements created. Please go back to Step 2.")
            if st.button("⬅️ Back to Step 2"):
                st.session_state.step = 2
                st.rerun()
    else:
        # Default to step 1
        st.session_state.step = 1
        st.rerun()

    # Sidebar with session information
    with st.sidebar:
        st.header("📋 Session Info")

        if st.session_state.data_source is not None:
            try:
                row_count = len(st.session_state.data_source)
                st.success(f"✅ Data loaded: {row_count} rows")
            except Exception as e:
                st.success("✅ Data loaded")
        else:
            st.info("No data loaded")

        if st.session_state.label_elements:
            st.success(f"✅ Design elements: {len(st.session_state.label_elements)}")
            for i, element in enumerate(st.session_state.label_elements):
                st.write(f"• {element.element_type.title()} {i+1}")
        else:
            st.info("No design elements")

        st.markdown("---")
        st.subheader("🔧 Quick Actions")

        if st.button("🔄 Reset All", use_container_width=True):
            for key in list(st.session_state.keys()):
                del st.session_state[key]
            st.rerun()

        if st.session_state.data_source is not None:
            if st.button("📊 View Data", use_container_width=True):
                with st.expander("Data Preview", expanded=True):
                    try:
                        if hasattr(st.session_state.data_source, 'to_pandas'):
                            preview_df = st.session_state.data_source.head(10).to_pandas()
                        else:
                            preview_df = st.session_state.data_source.head(10)
                        st.dataframe(preview_df)
                    except Exception as e:
                        st.error(f"Error displaying data: {e}")
                        st.write("Data source available but cannot display preview")

if __name__ == "__main__":
    main()
